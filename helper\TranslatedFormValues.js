import { useLanguage } from '../contexts/LanguageContext';

export const useTranslatedFormValues = () => {
  const { texts } = useLanguage();

  return {
    maritalStatusValues: [
      texts.NEVER_MARRIED,
      texts.WIDOWER,
      texts.AWAITING_DIVORCE,
      texts.DIVORCED,
    ],
    districtValues: [
      texts.THIRUVANANTHAPURAM,
      texts.KOLLAM,
      texts.PATH<PERSON>AMTHITTA,
      texts.ALAPPUZHA,
      texts.KOTTAYAM,
      texts.IDUKKI,
      texts.ERNAKULAM,
      texts.THRISSUR,
      texts.PALAKKAD,
      texts.MALAPPURAM,
      texts.KOZHIKODE,
      texts.WAYANAD,
      texts.KANNUR,
      texts.KASARAGOD,
    ],
    highestEducationValues: [
      texts.DOCTORATE,
      texts.MASTERS_DEGREE,
      texts.BACHELORS_DEGREE,
      texts.MEDICAL_UG,
      texts.MEDICAL_PG,
      texts.GENERAL_NURSING,
      texts.LAW,
      texts.CIVIL_SERVICE,
      texts.UGC,
      texts.MANAGEMENT_BUSINESS,
      texts.FINANCE_CHARTERED,
      texts.COMPUTER_IT,
      texts.SCIENCE_ENGINEERING,
      texts.ARTS_MEDIA,
      texts.TEACHING_EDUCATION,
      texts.ITI,
      texts.MS,
      texts.DIPLOMA,
      texts.SCHOOL_LEVEL,
      texts.OTHER
    ],
    religionValues: [
      texts.HINDU,
      texts.MUSLIM,
      texts.CHRISTIAN,
      texts.JAIN,
      texts.JEWISH,
      texts.INTER_RELIGION,
      texts.NO_RELIGION,
      texts.OTHER
    ],
    employedInValues: [
      texts.GOVERNMENT,
      texts.PRIVATE,
      texts.BUSINESS,
      texts.SELF_EMPLOYED,
      texts.NOT_WORKING,
      texts.OTHER
    ],
    salaryValues: [
      texts.BELOW_2_LAKH,
      texts.TWO_TO_FIVE_LAKH,
      texts.FIVE_TO_TEN_LAKH,
      texts.TEN_TO_TWENTY_LAKH,
      texts.ABOVE_TWENTY_LAKH,
    ],
    // Keep original values for data consistency
    originalFormValues: {
      maritalStatusValues: [
        'Never married',
        'Widower',
        'Awaiting divorce',
        'Divorced',
      ],
      districtValues: [
        'Thiruvananthapuram',
        'Kollam',
        'Pathanamthitta',
        'Alappuzha',
        'Kottayam',
        'Idukki',
        'Ernakulam',
        'Thrissur',
        'Palakkad',
        'Malappuram',
        'Kozhikode',
        'Wayanad',
        'Kannur',
        'Kasaragod',
      ],
      highestEducationValues: [
        "Doctorate (PhD, MD, etc.)",
        "Master's Degree",
        "Bachelor's Degree",
        "Medical UG",
        "Medical PG",
        "General Nursing",
        "Law",
        "Civil Service",
        "UGC",
        "Management / Business",
        "Finance / Chartered Courses",
        "Computer / IT",
        "Science / Engineering",
        "Arts / Media / Communication",
        "Teaching / Education",
        "ITI",
        "MS",
        "Diploma",
        "School-Level",
        "Other"
      ],
      religionValues: [
        'Hindu',
        'Muslim',
        'Christian',
        'Jain',
        'Jewish',
        'Inter-Religion',
        'No Religion',
        'Other'
      ],
      employedInValues: [
        'Government',
        'Private',
        'Business',
        'Self Employed',
        'Not Working',
        'Other'
      ],
      salaryValues: [
        'Below 2 Lakh',
        '2-5 Lakh',
        '5-10 Lakh',
        '10-20 Lakh',
        'Above 20 Lakh',
      ]
    }
  };
};
