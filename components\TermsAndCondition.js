import React, { useState } from 'react';
import { Text, View, Modal, StyleSheet, TouchableOpacity } from 'react-native';
import {properties} from '../helper/Property';
import {variables} from '../styles/Variables';
import registerStyles from '../styles/RegisterStyles';

const TermsAndConditionsPopup = () => {
  const [modalVisible, setModalVisible] = useState(false);

  return (
    <View style={styles.container}>

      <View style={registerStyles.terms}>
        <Text style={registerStyles.termsAgreeText}>{properties.I_AGREE_TERMS}</Text>
        <Text onPress={() => setModalVisible(true)} style={registerStyles.termsText}>{properties.TEARMS_AND_CONDITION}</Text>
      </View>
      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>{properties.TERMS_TITLE}</Text>
            <Text>{properties.TERMS_CONTENT}</Text>
            <TouchableOpacity onPress={() => setModalVisible(false)}>
              <Text style={styles.closeButton}>{properties.CLOSE}</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  termsLink: {
    textDecorationLine: 'underline',
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: variables.modelBGColor,
  },
  modalContent: {
    backgroundColor: variables.white,
    padding: 20,
    borderRadius: 10,
    alignItems: 'center',
    elevation: 5,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  closeButton: {
    textDecorationLine: 'underline',
    marginTop: 10,
  },
});

export default TermsAndConditionsPopup;
