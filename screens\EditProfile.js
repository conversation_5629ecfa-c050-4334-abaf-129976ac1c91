import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
  Alert,
} from 'react-native';
import { Picker } from '@react-native-picker/picker';
import firestore from '@react-native-firebase/firestore';
import auth from '@react-native-firebase/auth';
import formValues from '../helper/FormValues';
import { variables } from '../styles/Variables';

// Import form values from the centralized helper file
const {
  maritalStatusValues,
  districtValues,
  highestEducationValues,
  occupationValues,
  religionValues,
  employedInValues,
  salaryValues,
  starValues,
  casteData
} = formValues;

// Additional options for habits and other fields
const drinkingHabitsOptions = ['Never', 'Occasionally', 'Frequently'];
const smokingHabitsOptions = ['Never', 'Occasionally', 'Frequently'];
const eatingHabitsOptions = ['Vegetarian', 'Non-Vegetarian', 'Eggetarian'];
const motherTongueOptions = ['Malayalam', 'Tamil', 'English'];
const physicalStatusOptions = ['Normal', 'Physically Challenged'];


const EditProfile = ({ navigation }) => {
  // Helper function to transform options array to picker format
  const transformToOptions = (optionsArray) => {
    return optionsArray.map((option) => ({
      label: option,
      value: option.includes('Select') ? '' : option,
    }));
  };

  // State for profile data
  // State for validation errors
  const [errors, setErrors] = useState({});

  const [profileData, setProfileData] = useState({
    fullName: '',
    age: '',
    dateOfBirth: '',
    email: '',
    gender: '',
    phoneNumber: '',
    height: '',
    weight: '',
    maritalStatus: '',
    motherTongue: '',
    physicalStatus: '',
    eatingHabits: '',
    drinkingHabits: '',
    smokingHabits: '',
    highestEducation: '',
    occupation: '',
    salary: '',
    religion: '',
    caste: '',
    star: '',
    country: '',
    state: '',
    district: '',
    city: '',
    employedIn: '',
    aboutMe: '',
    father: '',
    mother: '',
    siblings: '',
    // Custom fields
    customReligion: '',
    customCaste: '',
    customEducation: '',
    customEmployedIn: '',
    customOccupation: '',
    // UI control flags for showing text inputs instead of dropdowns
    showCustomReligion: false,
    showCustomCaste: false,
    showCustomEducation: false,
    showCustomEmployedIn: false,
    showCustomOccupation: false,
  });

  useEffect(() => {
    const fetchUserProfile = async () => {
      try {
        const user = auth().currentUser;
        const userProfileDoc = await firestore().collection('users').doc(user.uid).get();
        if (userProfileDoc.exists) {
          const userData = userProfileDoc.data();

          // Sanitize the data to replace any undefined values
          const sanitizedData = {};
          Object.entries(userData).forEach(([key, value]) => {
            if (value === undefined) {
              // Replace undefined with appropriate default values
              if (key === 'height' || key === 'weight') {
                sanitizedData[key] = null;
              } else {
                sanitizedData[key] = '';
              }
              console.warn(`Replaced undefined value in field: ${key}`);
            } else {
              sanitizedData[key] = value;
            }
          });

          // Ensure all custom fields have at least empty string values in the sanitized data
          if (sanitizedData.religion === 'Other' && !sanitizedData.customReligion) {
            sanitizedData.customReligion = '';
          }

          if (sanitizedData.caste === 'Other' && !sanitizedData.customCaste) {
            sanitizedData.customCaste = '';
          }

          if (!sanitizedData.customEducation) {
            sanitizedData.customEducation = '';
          }

          if (sanitizedData.employedIn === 'Other' && !sanitizedData.customEmployedIn) {
            sanitizedData.customEmployedIn = '';
          }

          if (sanitizedData.occupation === 'Other' && !sanitizedData.customOccupation) {
            sanitizedData.customOccupation = '';
          }

          // Set UI control flags based on fetched data
          const updatedData = {
            ...sanitizedData,
            showCustomReligion: sanitizedData.religion === 'Other',
            showCustomCaste: sanitizedData.caste === 'Other',
            showCustomEducation: true, // Always show custom education field
            showCustomEmployedIn: sanitizedData.employedIn === 'Other',
            showCustomOccupation: sanitizedData.occupation === 'Other',
          };

          setProfileData(updatedData);
          console.log('Profile data loaded with custom fields:', updatedData);
        }
      } catch (error) {
        console.error('Error loading profile:', error);
        Alert.alert('Error', `Failed to load profile: ${error.message}`);
      }
    };
    fetchUserProfile();
  }, []);

  const handleInputChange = (field, value) => {
    // Clear any existing error for this field
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }

    // Helper function to check if a string contains numbers
    const containsNumbers = (text) => /\d/.test(text);

    // Handle special dropdown fields that might show custom input fields
    if (field === 'religion') {
      setProfileData(prevData => ({
        ...prevData,
        [field]: value,
        showCustomReligion: value === 'Other',
        // If religion is "Other", automatically set caste to "Other" as well
        // Otherwise, reset caste when religion changes
        caste: value === 'Other' ? 'Other' : '',
        showCustomCaste: value === 'Other',
        customCaste: value === 'Other' ? prevData.customCaste : ''
      }));
      return;
    }

    if (field === 'caste') {
      setProfileData(prevData => ({
        ...prevData,
        [field]: value,
        showCustomCaste: value === 'Other'
      }));
      return;
    }

    if (field === 'highestEducation') {
      setProfileData(prevData => ({
        ...prevData,
        [field]: value,
        // Always show custom education field
        showCustomEducation: true
      }));
      return;
    }

    if (field === 'employedIn') {
      setProfileData(prevData => ({
        ...prevData,
        [field]: value,
        showCustomEmployedIn: value === 'Other'
      }));
      return;
    }

    if (field === 'occupation') {
      setProfileData(prevData => ({
        ...prevData,
        [field]: value,
        showCustomOccupation: value === 'Other'
      }));
      return;
    }

    // Validate height and weight fields
    if (field === 'height' || field === 'weight') {
      // Check if value contains non-numeric characters
      if (value && !/^\d*$/.test(value)) {
        setErrors(prev => ({ ...prev, [field]: `${field.charAt(0).toUpperCase() + field.slice(1)} should only contain numbers` }));
      }
      // Check if value exceeds 3 digits
      else if (value && value.length > 3) {
        setErrors(prev => ({ ...prev, [field]: `${field.charAt(0).toUpperCase() + field.slice(1)} should not exceed 3 digits` }));
        // Truncate to 3 digits
        value = value.substring(0, 3);
      }
      // Convert to number if valid
      else if (value) {
        value = Number(value);
      }
    }

    // Real-time validation for text-only fields (excluding phone number)
    const textOnlyFields = ['fullName', 'city', 'father', 'mother', 'siblings', 'aboutMe',
                           'customReligion', 'customCaste', 'customEducation', 'customEmployedIn', 'customOccupation'];
    if (textOnlyFields.includes(field)) {
      if (containsNumbers(value)) {
        // Create a more user-friendly field name for error messages
        let fieldName = field;
        if (field.startsWith('custom')) {
          // Remove 'custom' prefix and capitalize the first letter
          fieldName = field.replace('custom', '');
          fieldName = fieldName.charAt(0).toUpperCase() + fieldName.slice(1);
        } else {
          // Just capitalize the first letter
          fieldName = field.charAt(0).toUpperCase() + field.slice(1);
        }

        setErrors(prev => ({ ...prev, [field]: `${fieldName} should not contain numbers` }));

        // Return early without updating the field value if it contains numbers
        return;
      }
    }

    setProfileData((prevData) => ({
      ...prevData,
      [field]: value,
    }));
  };

  const validateForm = () => {
    const newErrors = {};
    let isValid = true;

    // Validate height
    if (profileData.height) {
      if (isNaN(profileData.height)) {
        newErrors.height = 'Height should only contain numbers';
        isValid = false;
      } else if (String(profileData.height).length > 3) {
        newErrors.height = 'Height should not exceed 3 digits';
        isValid = false;
      }
    }

    // Validate weight
    if (profileData.weight) {
      if (isNaN(profileData.weight)) {
        newErrors.weight = 'Weight should only contain numbers';
        isValid = false;
      } else if (String(profileData.weight).length > 3) {
        newErrors.weight = 'Weight should not exceed 3 digits';
        isValid = false;
      }
    }

    // Helper function to check if a string contains numbers
    const containsNumbers = (text) => /\d/.test(text);

    // Define text-only fields and their character limits
    const textFieldLimits = {
      fullName: 50,
      city: 50,
      father: 100,
      mother: 100,
      siblings: 200,
      aboutMe: 300,
      customReligion: 50,
      customCaste: 50,
      customEducation: 100,
      customEmployedIn: 50,
      customOccupation: 50
    };

    // Validate all text-only fields
    Object.entries(textFieldLimits).forEach(([field, limit]) => {
      // Skip validation for custom fields that aren't being used
      if (field === 'customReligion' && !profileData.showCustomReligion) return;
      if (field === 'customCaste' && !profileData.showCustomCaste) return;
      if (field === 'customEmployedIn' && !profileData.showCustomEmployedIn) return;
      if (field === 'customOccupation' && !profileData.showCustomOccupation) return;

      if (profileData[field]) {
        // Check character limit
        if (profileData[field].length > limit) {
          newErrors[field] = `${field.charAt(0).toUpperCase() + field.slice(1)} should not exceed ${limit} characters`;
          isValid = false;
        }

        // Check for numbers (except in phoneNumber)
        if (containsNumbers(profileData[field])) {
          // Create a more user-friendly field name for error messages
          let fieldName = field;
          if (field.startsWith('custom')) {
            // Remove 'custom' prefix and capitalize the first letter
            fieldName = field.replace('custom', '');
            fieldName = fieldName.charAt(0).toUpperCase() + fieldName.slice(1);
          } else {
            // Just capitalize the first letter
            fieldName = field.charAt(0).toUpperCase() + field.slice(1);
          }

          newErrors[field] = `${fieldName} should not contain numbers`;
          isValid = false;
        }
      }
    });

    setErrors(newErrors);
    return isValid;
  };

  // Helper function to recursively check for undefined values in an object
  const findUndefinedValues = (obj, path = '', result = {}) => {
    if (!obj || typeof obj !== 'object') return result;

    Object.entries(obj).forEach(([key, value]) => {
      const currentPath = path ? `${path}.${key}` : key;

      if (value === undefined) {
        result[currentPath] = undefined;
      } else if (value !== null && typeof value === 'object' && !Array.isArray(value)) {
        findUndefinedValues(value, currentPath, result);
      }
    });

    return result;
  };

  const handleSubmit = async () => {
    // Additional validation for required custom fields when "Other" is selected
    const additionalErrors = {};
    let hasAdditionalErrors = false;

    // Check if religion is "Other" but no custom religion is provided
    if (profileData.religion === 'Other' && !profileData.customReligion) {
      additionalErrors.customReligion = 'Please enter your religion';
      hasAdditionalErrors = true;
    }

    // Check if caste is "Other" but no custom caste is provided
    if (profileData.caste === 'Other' && !profileData.customCaste &&
        !(profileData.religion === 'Other' || profileData.religion === 'Jain' ||
          profileData.religion === 'Jewish' || profileData.religion === 'Inter-Religion' ||
          profileData.religion === 'No Religion')) {
      additionalErrors.customCaste = 'Please enter your caste';
      hasAdditionalErrors = true;
    }

    // Check if employedIn is "Other" but no custom employedIn is provided
    if (profileData.employedIn === 'Other' && !profileData.customEmployedIn) {
      additionalErrors.customEmployedIn = 'Please enter your employment type';
      hasAdditionalErrors = true;
    }

    // Check if occupation is "Other" but no custom occupation is provided
    if (profileData.occupation === 'Other' && !profileData.customOccupation) {
      additionalErrors.customOccupation = 'Please enter your occupation';
      hasAdditionalErrors = true;
    }

    // If there are additional errors, update the errors state and return
    if (hasAdditionalErrors) {
      setErrors(prev => ({ ...prev, ...additionalErrors }));
      Alert.alert('Validation Error', 'Please fill in all required custom fields.');
      return;
    }

    // Run the standard validation
    if (!validateForm()) {
      Alert.alert('Validation Error', 'Please correct the errors before submitting.');
      return;
    }

    try {
      const user = auth().currentUser;

      // Convert height to number if it's a string and prepare data
      // First, create a clean copy of profileData without any undefined values
      const cleanProfileData = {};

      // Copy all properties from profileData, replacing undefined with empty strings or null
      Object.keys(profileData).forEach(key => {
        // Skip UI control flags (they start with "show")
        if (key.startsWith('show')) return;

        // Replace undefined with appropriate default values
        if (profileData[key] === undefined) {
          // For numeric fields
          if (key === 'height' || key === 'weight') {
            cleanProfileData[key] = null;
          }
          // For all other fields
          else {
            cleanProfileData[key] = '';
          }
        } else {
          cleanProfileData[key] = profileData[key];
        }
      });

      // Now create the final data object with all the necessary transformations
      const updatedProfileData = {
        ...cleanProfileData,
        // Convert string values to numbers for numeric fields
        height: cleanProfileData.height ? Number(cleanProfileData.height) : null,
        weight: cleanProfileData.weight ? Number(cleanProfileData.weight) : null,
        // Add a timestamp to track when the profile was last updated
        lastUpdated: firestore.FieldValue.serverTimestamp(),

        // If religion is "Other", ensure caste is set to "Other" as well
        caste: cleanProfileData.religion === 'Other' ? 'Other' : cleanProfileData.caste,

        // Ensure custom fields are properly saved
        customReligion: cleanProfileData.religion === 'Other' ? cleanProfileData.customReligion || '' : '',
        // Handle custom caste based on religion and caste values
        customCaste: cleanProfileData.religion === 'Other' ? cleanProfileData.customCaste || '' :
                    (cleanProfileData.caste === 'Other' ? cleanProfileData.customCaste || '' : ''),
        customEducation: cleanProfileData.customEducation || '',
        customEmployedIn: cleanProfileData.employedIn === 'Other' ? cleanProfileData.customEmployedIn || '' : '',
        customOccupation: cleanProfileData.occupation === 'Other' ? cleanProfileData.customOccupation || '' : '',
      };

      // Log the data being saved for debugging
      console.log('Saving profile data:', JSON.stringify(updatedProfileData));

      // Final check for any undefined values
      const undefinedFields = [];
      Object.entries(updatedProfileData).forEach(([key, value]) => {
        if (value === undefined) {
          undefinedFields.push(key);
          // Replace undefined with empty string or null
          if (key === 'height' || key === 'weight') {
            updatedProfileData[key] = null;
          } else {
            updatedProfileData[key] = '';
          }
        }
      });

      if (undefinedFields.length > 0) {
        console.warn('Fixed undefined fields before saving:', undefinedFields);
      }

      try {
        await firestore().collection('users').doc(user.uid).set(updatedProfileData, { merge: true });
        Alert.alert('Success', 'Profile updated successfully!');

        // Go back to previous screen
        navigation.goBack();
      } catch (error) {
        console.error('Firestore error details:', error);

        // Check for undefined values in the data using our helper function
        const undefinedValues = findUndefinedValues(updatedProfileData);
        const problematicFields = Object.keys(undefinedValues);

        if (problematicFields.length > 0) {
          console.error('Found undefined values in:', problematicFields);

          // Create a sanitized version of the data with all undefined values replaced
          const sanitizedData = JSON.parse(JSON.stringify(updatedProfileData));

          // Try again with the sanitized data
          try {
            await firestore().collection('users').doc(user.uid).set(sanitizedData, { merge: true });
            Alert.alert('Success', 'Profile updated successfully after fixing undefined values.');
            navigation.goBack();
          } catch (retryError) {
            console.error('Retry error:', retryError);
            Alert.alert('Error', `Failed to update profile: ${retryError.message}`);
          }
        } else {
          Alert.alert('Error', `Failed to update profile: ${error.message}`);
        }
      }
    } catch (error) {
      console.error('Outer error:', error);
      Alert.alert('Error', `Failed to update profile: ${error.message}`);
    }
  };

  return (
    <ScrollView contentContainerStyle={styles.container} showsVerticalScrollIndicator={false}>
      <Text style={styles.heading}>Personal Details</Text>
      <View style={styles.inputContainerHead}>
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Full Name</Text>
          <TextInput
            style={errors.fullName ? styles.inputError : styles.input}
            placeholder="Full Name (text only)"
            value={profileData.fullName}
            onChangeText={(value) => handleInputChange('fullName', value)}
          />
          {errors.fullName && <Text style={styles.errorText}>{errors.fullName}</Text>}
        </View>
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Phone Number</Text>
          <TextInput
            style={styles.input}
            placeholder="Phone Number"
            value={profileData.phoneNumber}
            onChangeText={(value) => handleInputChange('phoneNumber', value)}
            keyboardType="numeric"
          />
        </View>
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Email</Text>
          <TextInput
            style={styles.input}
            placeholder="Email"
            value={profileData.email}
            onChangeText={(value) => handleInputChange('email', value)}
          />
        </View>
      </View>

      <Text style={styles.heading}>About Me</Text>
      <View style={styles.inputContainerHead}>
        <View style={styles.inputContainer}>
          <TextInput
            style={[errors.aboutMe ? styles.inputError : styles.input, { height: 100, textAlignVertical: 'top' }]}
            placeholder="Few words about you (text only)"
            value={profileData.aboutMe}
            onChangeText={(text) => {
              if (text.length <= 300) {
                handleInputChange('aboutMe', text);
              }
            }}
            multiline
            numberOfLines={4}
            maxLength={300}
          />
          <Text style={styles.charCount}>
            {profileData.aboutMe ? profileData.aboutMe.length : 0}/300 characters
          </Text>
          {errors.aboutMe && <Text style={styles.errorText}>{errors.aboutMe}</Text>}
        </View>
      </View>

      <Text style={styles.heading}>Basic Details</Text>
      <View style={styles.inputContainerHead}>
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Height (in cm)</Text>
          <TextInput
            style={errors.height ? styles.inputError : styles.input}
            placeholder="Height (max 3 digits)"
            value={profileData.height ? profileData.height.toString() : ''}
            onChangeText={(value) => handleInputChange('height', value)}
            keyboardType="numeric"
            maxLength={3}
          />
          <Text style={styles.helperText}>Enter your height in centimeters (e.g., 175)</Text>
          {errors.height && <Text style={styles.errorText}>{errors.height}</Text>}
        </View>
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Weight (in kg)</Text>
          <TextInput
            style={errors.weight ? styles.inputError : styles.input}
            placeholder="Weight (max 3 digits)"
            value={profileData.weight ? profileData.weight.toString() : ''}
            onChangeText={(value) => handleInputChange('weight', value)}
            keyboardType="numeric"
            maxLength={3}
          />
          <Text style={styles.helperText}>Enter your weight in kilograms (e.g., 65)</Text>
          {errors.weight && <Text style={styles.errorText}>{errors.weight}</Text>}
        </View>
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Marital Status</Text>
          <View style={styles.pickerContainer}>
            <Picker
              selectedValue={profileData.maritalStatus}
              onValueChange={(value) => handleInputChange('maritalStatus', value)}>
              <Picker.Item label="Select Marital Status" value="" />
              {maritalStatusValues.map((status, index) => (
                <Picker.Item key={index} label={status} value={status} />
              ))}
            </Picker>
          </View>
        </View>
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Mother Tongue</Text>
          <View style={styles.pickerContainer}>
            <Picker
              selectedValue={profileData.motherTongue}
              onValueChange={(value) => handleInputChange('motherTongue', value)}>
              <Picker.Item label="Select Mother Tongue" value="" />
              {motherTongueOptions.map((option, index) => (
                <Picker.Item key={index} label={option} value={option} />
              ))}
            </Picker>
          </View>
        </View>
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Physical Status</Text>
          <View style={styles.pickerContainer}>
            <Picker
              selectedValue={profileData.physicalStatus}
              onValueChange={(value) => handleInputChange('physicalStatus', value)}>
              <Picker.Item label="Select Physical Status" value="" />
              {physicalStatusOptions.map((option, index) => (
                <Picker.Item key={index} label={option} value={option} />
              ))}
            </Picker>
          </View>
        </View>
        {/* Star Dropdown */}
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Star</Text>
          <View style={styles.pickerContainer}>
            <Picker
              selectedValue={profileData.star}
              onValueChange={(value) => handleInputChange('star', value)}
            >
              <Picker.Item label="Select Star" value="" />
              {starValues.map((star, index) => (
                <Picker.Item key={index} label={star} value={star} />
              ))}
            </Picker>
          </View>
        </View>
      </View>
      {/* Add more fields similar to above as per your requirement */}

      <Text style={styles.heading}>Professional Details</Text>
      <View style={styles.inputContainerHead}>
        {/* Education Dropdown */}
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Education</Text>
          <View style={styles.pickerContainer}>
            <Picker
              selectedValue={profileData.highestEducation}
              onValueChange={(value) => handleInputChange('highestEducation', value)}
            >
              <Picker.Item label="Select Education" value="" />
              {highestEducationValues.map((education, index) => (
                <Picker.Item key={index} label={education} value={education} />
              ))}
            </Picker>
          </View>
        </View>

        {/* Custom Education Input - always shown */}
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Education Details</Text>
          <TextInput
            style={errors.customEducation ? styles.inputError : styles.input}
            placeholder="Enter education details (text only)"
            value={profileData.customEducation}
            onChangeText={(value) => handleInputChange('customEducation', value)}
          />
          {errors.customEducation && <Text style={styles.errorText}>{errors.customEducation}</Text>}
        </View>

        {/* Occupation Dropdown */}
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Occupation</Text>
          <View style={styles.pickerContainer}>
            <Picker
              selectedValue={profileData.occupation}
              onValueChange={(value) => handleInputChange('occupation', value)}
            >
              <Picker.Item label="Select Occupation" value="" />
              {occupationValues.map((occupation, index) => (
                <Picker.Item key={index} label={occupation} value={occupation} />
              ))}
            </Picker>
          </View>
        </View>

        {/* Custom Occupation Input - shows when "Other" is selected */}
        {profileData.showCustomOccupation && (
          <View style={styles.inputContainer}>
            <Text style={styles.label}>Custom Occupation</Text>
            <TextInput
              style={errors.customOccupation ? styles.inputError : styles.input}
              placeholder="Enter your occupation (text only)"
              value={profileData.customOccupation}
              onChangeText={(value) => handleInputChange('customOccupation', value)}
            />
            {errors.customOccupation && <Text style={styles.errorText}>{errors.customOccupation}</Text>}
          </View>
        )}

        {/* Annual incom Dropdown */}
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Salary</Text>
          <View style={styles.pickerContainer}>
            <Picker
              selectedValue={profileData.salary}
              onValueChange={(value) => handleInputChange('salary', value)}
            >
              <Picker.Item label="Select Salary" value="" />
              {salaryValues.map((salary, index) => (
                <Picker.Item key={index} label={salary} value={salary} />
              ))}
            </Picker>
          </View>
        </View>
      </View>
      <Text style={styles.heading}>Religious Details</Text>
      <View style={styles.inputContainerHead}>
        <View style={styles.container}>
          {/* Religion Picker */}
          <View style={styles.inputContainer}>
            <Text style={styles.label}>Religion</Text>
            <View style={styles.pickerContainer}>
              <Picker
                selectedValue={profileData.religion}
                onValueChange={(value) => {
                  handleInputChange('religion', value);
                }}
                style={styles.picker}
              >
                <Picker.Item label="Select Religion" value="" />
                {religionValues.map((religion, index) => (
                  <Picker.Item key={index} label={religion} value={religion} />
                ))}
              </Picker>
            </View>
          </View>

          {/* Custom Religion Input - shows when "Other" is selected */}
          {profileData.showCustomReligion && (
            <View style={styles.inputContainer}>
              <Text style={styles.label}>Custom Religion</Text>
              <TextInput
                style={errors.customReligion ? styles.inputError : styles.input}
                placeholder="Enter your religion (text only)"
                value={profileData.customReligion}
                onChangeText={(value) => handleInputChange('customReligion', value)}
              />
              {errors.customReligion && <Text style={styles.errorText}>{errors.customReligion}</Text>}
            </View>
          )}

          {/* Caste Picker - shows options based on selected religion */}
          {profileData.religion && !(profileData.religion === 'Other' || profileData.religion === 'Jain' || profileData.religion === 'Jewish' || profileData.religion === 'Inter-Religion' || profileData.religion === 'No Religion') && (
            <View style={styles.inputContainer}>
              <Text style={styles.label}>Caste</Text>
              <View style={styles.pickerContainer}>
                <Picker
                  selectedValue={profileData.caste}
                  onValueChange={(value) => handleInputChange('caste', value)}
                  style={styles.picker}
                >
                  <Picker.Item label="Select Caste" value="" />
                  {(casteData[profileData.religion] || []).map((caste, index) => (
                    <Picker.Item key={index} label={caste} value={caste} />
                  ))}
                </Picker>
              </View>
            </View>
          )}

          {/* Custom Caste Input - shows when "Other" is selected */}
          {profileData.showCustomCaste && (
            <View style={styles.inputContainer}>
              <Text style={styles.label}>Custom Caste</Text>
              <TextInput
                style={errors.customCaste ? styles.inputError : styles.input}
                placeholder="Enter your caste (text only)"
                value={profileData.customCaste}
                onChangeText={(value) => handleInputChange('customCaste', value)}
              />
              {errors.customCaste && <Text style={styles.errorText}>{errors.customCaste}</Text>}
            </View>
          )}


        </View>
      </View>
      <Text style={styles.heading}>Location</Text>
      <View style={styles.inputContainerHead}>
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Country</Text>
          <TextInput
            style={styles.input}
            placeholder="Country"
            editable={false}
            value='INDIA'
          />
        </View>
        <View style={styles.inputContainer}>
          <Text style={styles.label}>State</Text>
          <TextInput
            style={styles.input}
            placeholder="State"
            editable={false}
            value='KERALA'
          />
        </View>
        <View style={styles.inputContainer}>
          <Text style={styles.label}>District</Text>
          <View style={styles.pickerContainer}>
            <Picker
              selectedValue={profileData.district}
              onValueChange={(value) => handleInputChange('district', value)}
              style={styles.picker}
            >
              <Picker.Item label="Select District" value="" />
              {districtValues.map((district, index) => (
                <Picker.Item key={index} label={district} value={district} />
              ))}
            </Picker>
          </View>
        </View>
        <View style={styles.inputContainer}>
          <Text style={styles.label}>City</Text>
          <TextInput
            style={errors.city ? styles.inputError : styles.input}
            placeholder="Enter your city name (text only)"
            value={profileData.city}
            onChangeText={(value) => handleInputChange('city', value)}
          />
          {errors.city && <Text style={styles.errorText}>{errors.city}</Text>}
        </View>
      </View>

      <Text style={styles.heading}>Employment Details</Text>
      <View style={styles.inputContainerHead}>
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Employed In</Text>
          <View style={styles.pickerContainer}>
            <Picker
              selectedValue={profileData.employedIn}
              onValueChange={(value) => handleInputChange('employedIn', value)}
            >
              <Picker.Item label="Select Employment" value="" />
              {employedInValues.map((option, index) => (
                <Picker.Item key={index} label={option} value={option} />
              ))}
            </Picker>
          </View>
        </View>

        {/* Custom Employed In Input - shows when "Other" is selected */}
        {profileData.showCustomEmployedIn && (
          <View style={styles.inputContainer}>
            <Text style={styles.label}>Custom Employment</Text>
            <TextInput
              style={errors.customEmployedIn ? styles.inputError : styles.input}
              placeholder="Enter your employment (text only)"
              value={profileData.customEmployedIn}
              onChangeText={(value) => handleInputChange('customEmployedIn', value)}
            />
            {errors.customEmployedIn && <Text style={styles.errorText}>{errors.customEmployedIn}</Text>}
          </View>
        )}
      </View>

      <Text style={[styles.heading, { marginTop: 25 }]}>Habits & Lifestyle</Text>
      <View style={styles.inputContainerHead}>
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Eating Habits</Text>
          <View style={styles.pickerContainer}>
            <Picker
              selectedValue={profileData.eatingHabits}
              onValueChange={(value) => handleInputChange('eatingHabits', value)}
            >
              <Picker.Item label="Select Eating Habit" value="" />
              {eatingHabitsOptions.map((habit, index) => (
                <Picker.Item key={index} label={habit} value={habit} />
              ))}
            </Picker>
          </View>
        </View>

        <View style={styles.inputContainer}>
          <Text style={styles.label}>Drinking Habits</Text>
          <View style={styles.pickerContainer}>
            <Picker
              selectedValue={profileData.drinkingHabits}
              onValueChange={(value) => handleInputChange('drinkingHabits', value)}
            >
              <Picker.Item label="Select Drinking Habit" value="" />
              {drinkingHabitsOptions.map((habit, index) => (
                <Picker.Item key={index} label={habit} value={habit} />
              ))}
            </Picker>
          </View>
        </View>

        <View style={styles.inputContainer}>
          <Text style={styles.label}>Smoking Habit</Text>
          <View style={styles.pickerContainer}>
            <Picker
              selectedValue={profileData.smokingHabits}
              onValueChange={(value) => handleInputChange('smokingHabits', value)}
            >
              <Picker.Item label="Select Smoking Habit" value="" />
              {smokingHabitsOptions.map((habit, index) => (
                <Picker.Item key={index} label={habit} value={habit} />
              ))}
            </Picker>
          </View>
        </View>
      </View>

      <Text style={styles.heading}>Family Details</Text>
      <View style={styles.inputContainerHead}>
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Father</Text>
          <TextInput
            style={errors.father ? styles.inputError : styles.input}
            placeholder="Father's name"
            value={profileData.father}
            onChangeText={(value) => handleInputChange('father', value)}
          />
          {errors.father && <Text style={styles.errorText}>{errors.father}</Text>}
        </View>

        <View style={styles.inputContainer}>
          <Text style={styles.label}>Mother</Text>
          <TextInput
            style={errors.mother ? styles.inputError : styles.input}
            placeholder="Mother's name"
            value={profileData.mother}
            onChangeText={(value) => handleInputChange('mother', value)}
          />
          {errors.mother && <Text style={styles.errorText}>{errors.mother}</Text>}
        </View>

        <View style={styles.inputContainer}>
          <Text style={styles.label}>Siblings</Text>
          <TextInput
            style={errors.siblings ? styles.inputError : styles.input}
            placeholder="Details about siblings Names"
            value={profileData.siblings}
            onChangeText={(value) => handleInputChange('siblings', value)}
          />
          {errors.siblings && <Text style={styles.errorText}>{errors.siblings}</Text>}
        </View>
      </View>

      <TouchableOpacity onPress={handleSubmit} style={styles.saveButton}>
        <Text style={styles.saveButtonText}>Save</Text>
      </TouchableOpacity>
    </ScrollView >
  );
};

const styles = StyleSheet.create({
  container: {
    flexGrow: 1,
    backgroundColor: '#f9f9f9',
    padding: 20,
  },
  wrapper: {
    flex: 1,
    backgroundColor: '#f9f9f9',
  },
  heading: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 10,
    color: '#333',
  },
  inputContainerHead: {
    marginBottom: 25,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 8,
    padding: 15,
    backgroundColor: '#fff',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  inputContainer: {
    marginBottom: 15,
  },
  label: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 6,
    color: '#555',
  },
  input: {
    height: 45,
    borderColor: '#ddd',
    borderWidth: 1,
    borderRadius: 6,
    paddingHorizontal: 12,
    fontSize: 15,
    color: '#333',
    backgroundColor: '#fafafa',
  },
  pickerContainer: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 6,
    overflow: 'hidden',
    backgroundColor: '#fafafa',
  },
  picker: {
    height: 45,
    color: '#333',
  },
  charCount: {
    textAlign: 'right',
    fontSize: 12,
    color: '#888',
    marginTop: 4,
  },
  helperText: {
    fontSize: 12,
    color: '#888',
    marginTop: 4,
    marginLeft: 2,
  },
  errorText: {
    color: '#e53935',
    fontSize: 12,
    marginTop: 4,
    marginLeft: 2,
  },
  inputError: {
    height: 45,
    borderColor: '#e53935',
    borderWidth: 1,
    borderRadius: 6,
    paddingHorizontal: 12,
    fontSize: 15,
    color: '#333',
    backgroundColor: '#fff0f0',
  },
  saveButton: {
    backgroundColor: '#4caf50',
    paddingVertical: 14,
    borderRadius: 6,
    alignItems: 'center',
    marginTop: 20,
    marginBottom: 30,
  },
  saveButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default EditProfile;
