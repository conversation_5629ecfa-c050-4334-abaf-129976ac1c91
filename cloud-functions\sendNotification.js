// Cloud Function for sending FCM notifications
// This should be deployed to Firebase Functions

const functions = require('firebase-functions');
const admin = require('firebase-admin');

// Initialize Firebase Admin SDK
if (!admin.apps.length) {
  admin.initializeApp();
}

exports.sendNotification = functions.https.onCall(async (data, context) => {
  try {
    console.log('Received notification request:', JSON.stringify(data, null, 2));

    const { tokens, title, body, data: customData } = data;

    if (!tokens || !Array.isArray(tokens) || tokens.length === 0) {
      throw new functions.https.HttpsError('invalid-argument', 'Tokens array is required');
    }

    // Construct the FCM message
    const message = {
      notification: {
        title: title || 'New Notification',
        body: body || 'You have a new notification',
      },
      data: {
        // Convert all data values to strings (FCM requirement)
        screen: customData?.screen || 'Home',
        type: customData?.type || 'general',
        // Add any other custom data here
        ...Object.keys(customData || {}).reduce((acc, key) => {
          acc[key] = String(customData[key]);
          return acc;
        }, {})
      },
      // Send to multiple tokens
      tokens: tokens
    };

    console.log('Sending FCM message:', JSON.stringify(message, null, 2));

    // Send the message
    const response = await admin.messaging().sendMulticast(message);

    console.log('FCM response:', JSON.stringify(response, null, 2));

    // Check for failures
    if (response.failureCount > 0) {
      const failedTokens = [];
      response.responses.forEach((resp, idx) => {
        if (!resp.success) {
          failedTokens.push(tokens[idx]);
          console.error('Failed to send to token:', tokens[idx], 'Error:', resp.error);
        }
      });
      
      return {
        success: true,
        successCount: response.successCount,
        failureCount: response.failureCount,
        failedTokens: failedTokens
      };
    }

    return {
      success: true,
      successCount: response.successCount,
      failureCount: response.failureCount
    };

  } catch (error) {
    console.error('Error sending notification:', error);
    throw new functions.https.HttpsError('internal', 'Failed to send notification', error.message);
  }
});
