/**
 * @format
 */

import { AppRegistry } from 'react-native';
import App from './App';
import { name as appName } from './app.json';
import messaging from '@react-native-firebase/messaging';
import { displayNotification } from './components/NotificationService';

// Create a background message handler
messaging().setBackgroundMessageHandler(async (remoteMessage) => {
    console.log('Background message received:', remoteMessage);
    // Don't display notification here - FCM handles it automatically
    // Just log for debugging purposes
});

AppRegistry.registerComponent(appName, () => App);
