/**
 * @format
 */

import { AppRegistry } from 'react-native';
import App from './App';
import { name as appName } from './app.json';
import messaging from '@react-native-firebase/messaging';
import { displayNotification } from './components/NotificationService';

// Create a background message handler
messaging().setBackgroundMessageHandler(async (remoteMessage) => {
    await displayNotification(remoteMessage);
});

AppRegistry.registerComponent(appName, () => App);
