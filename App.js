import React, { useEffect } from 'react';
import { UserInteractionsProvider } from './contexts/UserInteractionsContext';
import AppNavigator from './components/AppNavigation';
import { requestPermission, createNotificationChannel } from './components/NotificationService';
import { PreferencesProvider } from './contexts/PreferencesContext';
import messaging from '@react-native-firebase/messaging';
import notifee, { EventType } from '@notifee/react-native';
import { navigateToLikedByOthers } from './components/NavigationService';

const App = () => {

  useEffect(() => {
    requestPermission();
    createNotificationChannel();

    // Handle notification clicks when app is in background/quit
    const handleNotificationClick = (remoteMessage) => {
      console.log('Notification clicked:', remoteMessage);
      if (remoteMessage?.data?.screen === 'LikedByOthers') {
        // Add a small delay to ensure navigation is ready
        setTimeout(() => {
          navigateToLikedByOthers();
        }, 1000);
      }
    };

    // Handle notification when app is opened from quit state
    messaging()
      .getInitialNotification()
      .then(handleNotificationClick);

    // Handle notification when app is in background
    const unsubscribe = messaging().onNotificationOpenedApp(handleNotificationClick);

    // Handle notifee notification clicks (for local notifications)
    const unsubscribeNotifee = notifee.onBackgroundEvent(async ({ type, detail }) => {
      if (type === EventType.PRESS) {
        console.log('Notifee notification clicked:', detail);
        if (detail.notification?.data?.screen === 'LikedByOthers') {
          setTimeout(() => {
            navigateToLikedByOthers();
          }, 1000);
        }
      }
    });

    return () => {
      unsubscribe();
      unsubscribeNotifee();
    };
  }, []);

  return (
    <UserInteractionsProvider>
      <PreferencesProvider>
        <AppNavigator />
      </PreferencesProvider>
    </UserInteractionsProvider >
  );
};

export default App;
