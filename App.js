import React, { useEffect } from 'react';
import { UserInteractionsProvider } from './contexts/UserInteractionsContext';
import AppNavigator from './components/AppNavigation';
import { requestPermission, createNotificationChannel } from './components/NotificationService';
import { PreferencesProvider } from './contexts/PreferencesContext';
import messaging from '@react-native-firebase/messaging';
import { navigationRef } from './components/NavigationService';

const App = () => {

  useEffect(() => {
    requestPermission();
    // Temporarily disable notification channel creation to test
    // createNotificationChannel();

    // Function to navigate to LikedByOthers tab
    const navigateToLikedByOthers = () => {
      if (navigationRef.isReady()) {
        navigationRef.reset({
          index: 0,
          routes: [
            {
              name: 'Home',
              params: { screen: 'LikedByOthers' }
            }
          ]
        });
      }
    };

    // Handle notification clicks
    const handleNotificationClick = (remoteMessage) => {
      console.log('=== FCM Notification clicked ===');
      console.log('Full remoteMessage:', JSON.stringify(remoteMessage, null, 2));

      // Check if it's a profile like notification
      const isProfileLike =
        remoteMessage?.data?.screen === 'LikedByOthers' ||
        remoteMessage?.data?.type === 'profile_like' ||
        remoteMessage?.notification?.body?.includes('liked your profile');

      console.log('isProfileLike:', isProfileLike);

      if (isProfileLike) {
        console.log('Navigating to LikedByOthers tab...');
        setTimeout(() => {
          navigateToLikedByOthers();
        }, 1500);
      }
    };

    // Handle notification when app is opened from quit state
    messaging()
      .getInitialNotification()
      .then((remoteMessage) => {
        if (remoteMessage) {
          handleNotificationClick(remoteMessage);
        }
      });

    // Handle notification when app is in background
    const unsubscribe = messaging().onNotificationOpenedApp(handleNotificationClick);

    return unsubscribe;
  }, []);

  return (
    <UserInteractionsProvider>
      <PreferencesProvider>
        <AppNavigator />
      </PreferencesProvider>
    </UserInteractionsProvider >
  );
};

export default App;
