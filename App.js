import React, { useEffect } from 'react';
import { UserInteractionsProvider } from './contexts/UserInteractionsContext';
import AppNavigator from './components/AppNavigation';
import { requestPermission, createNotificationChannel } from './components/NotificationService';
import { PreferencesProvider } from './contexts/PreferencesContext';

const App = () => {

  useEffect(() => {
    requestPermission();
    createNotificationChannel();
  }, []);

  return (
    <UserInteractionsProvider>
      <PreferencesProvider>
        <AppNavigator />
      </PreferencesProvider>
    </UserInteractionsProvider >
  );
};

export default App;
