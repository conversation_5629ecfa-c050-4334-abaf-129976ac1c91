import React, { useEffect } from 'react';
import { UserInteractionsProvider } from './contexts/UserInteractionsContext';
import AppNavigator from './components/AppNavigation';
import { requestPermission, createNotificationChannel } from './components/NotificationService';
import { PreferencesProvider } from './contexts/PreferencesContext';
import messaging from '@react-native-firebase/messaging';
import notifee, { EventType } from '@notifee/react-native';
import { navigateToLikedByOthers } from './components/NavigationService';

const App = () => {

  useEffect(() => {
    requestPermission();
    createNotificationChannel();

    // Handle notification clicks when app is in background/quit
    const handleNotificationClick = (remoteMessage) => {
      console.log('=== FCM Notification clicked ===');
      console.log('Full remoteMessage:', JSON.stringify(remoteMessage, null, 2));
      console.log('remoteMessage.data:', remoteMessage?.data);
      console.log('remoteMessage.data.screen:', remoteMessage?.data?.screen);
      console.log('remoteMessage.data.type:', remoteMessage?.data?.type);

      // Try multiple ways to detect the notification type
      const isProfileLike =
        remoteMessage?.data?.screen === 'LikedByOthers' ||
        remoteMessage?.data?.type === 'profile_like' ||
        remoteMessage?.notification?.title?.includes('liked your profile') ||
        remoteMessage?.notification?.body?.includes('liked your profile');

      console.log('isProfileLike:', isProfileLike);

      if (isProfileLike) {
        console.log('Detected profile like notification, navigating...');
        // Add a delay to ensure navigation is ready
        setTimeout(() => {
          navigateToLikedByOthers();
        }, 2000); // Increased delay
      } else {
        console.log('Not a profile like notification, going to home');
      }
    };

    // Handle notification when app is opened from quit state
    messaging()
      .getInitialNotification()
      .then((remoteMessage) => {
        if (remoteMessage) {
          console.log('App opened from quit state by notification');
          handleNotificationClick(remoteMessage);
        }
      });

    // Handle notification when app is in background
    const unsubscribe = messaging().onNotificationOpenedApp((remoteMessage) => {
      console.log('App opened from background by notification');
      handleNotificationClick(remoteMessage);
    });

    // Handle notifee notification clicks (for local notifications)
    const unsubscribeNotifee = notifee.onBackgroundEvent(async ({ type, detail }) => {
      if (type === EventType.PRESS) {
        console.log('Notifee notification clicked:', JSON.stringify(detail, null, 2));
        if (detail.notification?.data?.screen === 'LikedByOthers' || detail.notification?.data?.type === 'profile_like') {
          console.log('Detected profile like notification from Notifee, navigating...');
          setTimeout(() => {
            navigateToLikedByOthers();
          }, 1500);
        }
      }
    });

    return () => {
      unsubscribe();
      unsubscribeNotifee();
    };
  }, []);

  return (
    <UserInteractionsProvider>
      <PreferencesProvider>
        <AppNavigator />
      </PreferencesProvider>
    </UserInteractionsProvider >
  );
};

export default App;
