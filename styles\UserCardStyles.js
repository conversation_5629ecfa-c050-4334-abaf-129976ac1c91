import {StyleSheet} from 'react-native'

export default StyleSheet.create({
    card: {
      marginVertical: 6,
      marginHorizontal: 6,
      backgroundColor: '#ffffff',
      alignSelf: 'center',
      borderBottomWidth: 1,
      borderBottomColor: '#e0e0e0',
      paddingBottom: 12,
    },
    cardContent: {
      flexDirection: 'row',
      padding: 0,
      alignItems: 'center', // Center align items vertically
    },
    profileImage: {
      width: 120,
      height: 120,
      borderRadius: 8,
      marginRight: 16,
      borderWidth: 1,
      borderColor: '#e0e0e0',
      alignSelf: 'center', // Center the image
    },
    detailsContainer: {
      flex: 1,
      justifyContent: 'center', // Center content vertically
    },
    name: {
      fontSize: 15,
      fontWeight: 'bold',
      color: '#222',
      marginBottom: 6,
      letterSpacing: 0.5,
      paddingBottom: 2,
      alignSelf: 'flex-start',
    },
    infoRow: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 4,
      flexWrap: 'wrap',
    },
    ageLocation: {
      fontSize: 14,
      color: '#444',
      marginRight: 8,
      fontWeight: '500',
    },
    pillContainer: {
      backgroundColor: '#e8f4fd',
      paddingHorizontal: 10,
      paddingVertical: 4,
      borderRadius: 20,
      marginRight: 8,
      marginBottom: 4,
      borderWidth: 0,
      elevation: 1,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.05,
      shadowRadius: 1,
    },
    secondaryPill: {
      backgroundColor: '#f0f0f0',
    },
    pillText: {
      fontSize: 12,
      color: '#0077cc',
      fontWeight: '600',
    },
    info: {
      fontSize: 13,
      color: '#555',
      marginBottom: 3,
      lineHeight: 18,
    },
    infoLabel: {
      fontWeight: '600',
      color: '#333',
      marginRight: 3,
    },
    removeIcon: {
      position: 'absolute',
      top: 0,
      right: 0,
      backgroundColor: 'transparent',
      borderRadius: 20,
      width: 36,
      height: 36,
      alignItems: 'center',
      justifyContent: 'center',
      borderWidth: 1,
      borderColor: 'rgba(255, 59, 92, 0.3)',
    },
    favouriteText: {
      fontSize: 22,
      color: '#ff3b5c',
    },
  });

