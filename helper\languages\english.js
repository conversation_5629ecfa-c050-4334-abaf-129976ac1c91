export const englishTexts = {
  // Navigation & Screen Titles
  HOME: "Home",
  LOGIN: "Login",
  REGISTER: "Register",
  PROFILE: "Profile",
  SEARCH: "Search",
  FAVOURITE: "Favourite",
  LIKED_BY_OTHERS: "Liked By Others",
  EDIT_PROFILE: "Edit Profile",
  EDIT_PREFERENCES: "Edit Preferences",

  // Login Screen
  APP_NAME: "MalayaliMatch",
  APP_TAGLINE: "Trusted by <PERSON>, Loved by <PERSON><PERSON>!",
  EMAIL_LABEL: "Email",
  PASSWORD_LABEL: "Password",
  EMAIL_PLACEHOLDER: "Enter your email",
  PASSWORD_PLACEHOLDER: "Enter your password",
  LOGIN_BUTTON: "Login",
  LOGGING_IN: "Logging in...",
  NEW_USER_REGISTER: "New User? Register",
  FORGOT_PASSWORD: "Forgot Password?",
  OR: "OR",
  LANGUAGE_SWITCH: "മലയാളം",

  // Register Screen
  LETS_REGISTER: "Let's Register",
  STEP_BASIC_INFO: "Basic Info",
  STEP_PROFESSIONAL: "Professional",
  STEP_PERSONAL: "Personal",
  STEP_ABOUT_YOU: "About You",
  FULL_NAME: "Full Name",
  FULL_NAME_PLACEHOLDER: "Enter your full name",
  GENDER: "Gender",
  MALE: "Male",
  FEMALE: "Female",
  DATE_OF_BIRTH: "Date of Birth",
  SELECT_DATE: "Select Date",
  HEIGHT: "Height (in cm)",
  HEIGHT_PLACEHOLDER: "Enter height",
  MARITAL_STATUS: "Marital Status",
  SELECT_MARITAL_STATUS: "Select Marital Status",
  DISTRICT: "District",
  SELECT_DISTRICT: "Select District",
  RELIGION: "Religion",
  SELECT_RELIGION: "Select Religion",
  CASTE: "Caste",
  SELECT_CASTE: "Select Caste",
  STAR: "Star",
  SELECT_STAR: "Select Star",
  EMPLOYED_IN: "Employed In",
  SELECT_EMPLOYMENT: "Select Employment",
  EDUCATION: "Education",
  SELECT_EDUCATION: "Select Education",
  OCCUPATION: "Occupation",
  SELECT_OCCUPATION: "Select Occupation",
  SALARY: "Salary",
  SELECT_SALARY: "Select Salary",
  ABOUT_ME: "About Me",
  ABOUT_ME_PLACEHOLDER: "Tell us about yourself",
  PROMO_CODE: "Promo Code",
  PROMO_CODE_PLACEHOLDER: "Enter promo code (optional)",
  EMAIL: "Email",
  EMAIL_PLACEHOLDER: "Enter your email",
  PHONE_NUMBER: "Phone Number",
  PHONE_PLACEHOLDER: "Enter phone number",
  PASSWORD: "Password",
  CONFIRM_PASSWORD: "Confirm Password",
  RETYPE_PASSWORD: "Re-type Password",
  PASSWORD_HELP: "Password must be at least 6 characters",
  NEXT: "Next",
  PREVIOUS: "Previous",
  SUBMIT: "Submit",
  SUBMITTING: "Submitting...",

  // Form Values - Marital Status
  NEVER_MARRIED: "Never married",
  WIDOWER: "Widower",
  AWAITING_DIVORCE: "Awaiting divorce",
  DIVORCED: "Divorced",

  // Form Values - Districts
  THIRUVANANTHAPURAM: "Thiruvananthapuram",
  KOLLAM: "Kollam",
  PATHANAMTHITTA: "Pathanamthitta",
  ALAPPUZHA: "Alappuzha",
  KOTTAYAM: "Kottayam",
  IDUKKI: "Idukki",
  ERNAKULAM: "Ernakulam",
  THRISSUR: "Thrissur",
  PALAKKAD: "Palakkad",
  MALAPPURAM: "Malappuram",
  KOZHIKODE: "Kozhikode",
  WAYANAD: "Wayanad",
  KANNUR: "Kannur",
  KASARAGOD: "Kasaragod",

  // Form Values - Education
  DOCTORATE: "Doctorate (PhD, MD, etc.)",
  MASTERS_DEGREE: "Master's Degree",
  BACHELORS_DEGREE: "Bachelor's Degree",
  MEDICAL_UG: "Medical UG",
  MEDICAL_PG: "Medical PG",
  GENERAL_NURSING: "General Nursing",
  LAW: "Law",
  CIVIL_SERVICE: "Civil Service",
  UGC: "UGC",
  MANAGEMENT_BUSINESS: "Management / Business",
  FINANCE_CHARTERED: "Finance / Chartered Courses",
  COMPUTER_IT: "Computer / IT",
  SCIENCE_ENGINEERING: "Science / Engineering",
  ARTS_MEDIA: "Arts / Media / Communication",
  TEACHING_EDUCATION: "Teaching / Education",
  ITI: "ITI",
  MS: "MS",
  DIPLOMA: "Diploma",
  SCHOOL_LEVEL: "School-Level",
  OTHER: "Other",

  // Form Values - Religion
  HINDU: "Hindu",
  MUSLIM: "Muslim",
  CHRISTIAN: "Christian",
  JAIN: "Jain",
  JEWISH: "Jewish",
  INTER_RELIGION: "Inter-Religion",
  NO_RELIGION: "No Religion",

  // Form Values - Employment
  GOVERNMENT: "Government",
  PRIVATE: "Private",
  BUSINESS: "Business",
  SELF_EMPLOYED: "Self Employed",
  NOT_WORKING: "Not Working",

  // Form Values - Salary
  BELOW_2_LAKH: "Below 2 Lakh",
  TWO_TO_FIVE_LAKH: "2-5 Lakh",
  FIVE_TO_TEN_LAKH: "5-10 Lakh",
  TEN_TO_TWENTY_LAKH: "10-20 Lakh",
  ABOVE_TWENTY_LAKH: "Above 20 Lakh",

  // Common
  YES: "Yes",
  NO: "No",
  SAVE: "Save",
  CANCEL: "Cancel",
  DELETE: "Delete",
  EDIT: "Edit",
  VIEW: "View",
  CLOSE: "Close",
  CONFIRM: "Confirm",
  SUCCESS: "Success",
  ERROR: "Error",
  WARNING: "Warning",
  INFO: "Info",
  LOADING: "Loading...",
  NO_DATA: "No Data Available",

  // Validation Messages
  REQUIRED_FIELD: "This field is required",
  INVALID_EMAIL: "Please enter a valid email address",
  PASSWORD_TOO_SHORT: "Password must be at least 6 characters long",
  PASSWORDS_DONT_MATCH: "Passwords do not match",
  INVALID_PHONE: "Please enter a valid phone number",
  SELECT_DATE_ERROR: "Please select a date",

  // Profile Screen
  PERSONAL_DETAILS: "Personal Details",
  PROFESSIONAL_DETAILS: "Professional Details",
  RELIGIOUS_DETAILS: "Religious Details",
  LOCATION: "Location",
  EMPLOYMENT_DETAILS: "Employment Details",
  HABITS_LIFESTYLE: "Habits & Lifestyle",
  FAMILY_DETAILS: "Family Details",

  // User Card
  AGE_YEARS: "years",
  AGE_NOT_AVAILABLE: "Age not available",
  CASTE_LABEL: "Caste:",
  STAR_LABEL: "Star:",
  EDUCATION_LABEL: "Education:",
  OCCUPATION_LABEL: "Occupation:",
  LOCATION_LABEL: "Location:",
  HEIGHT_LABEL: "Height:",

  // Home Screen
  PROFILE_COMPLETION: "Profile Completion",
  COMPLETE_PROFILE: "Complete Profile",
  UPDATE_PREFERENCES: "Update Preferences",
  TRY_SEARCH: "Try Search",
  NO_MORE_PROFILES: "You've seen all available matches!",
  LOOKING_FOR_MORE: "Looking for more compatible profiles?",

  // Search & Preferences
  AGE_RANGE: "Age Range",
  HEIGHT_RANGE: "Height Range",
  CLEAR_ALL: "Clear All",
  APPLY_FILTERS: "Apply Filters",
  NO_PREFERENCE: "No Preference",

  // Notifications
  PROFILE_LIKED: "Someone liked your profile!",
  NEW_MATCH: "You have a new match!",

  // Error Messages
  NETWORK_ERROR: "Network error. Please check your connection.",
  SERVER_ERROR: "Server error. Please try again later.",
  UNKNOWN_ERROR: "An unknown error occurred.",

  // Additional Form Fields
  MOTHER_TONGUE: "Mother Tongue",
  SELECT_MOTHER_TONGUE: "Select Mother Tongue",
  PHYSICAL_STATUS: "Physical Status",
  SELECT_PHYSICAL_STATUS: "Select Physical Status",
  EATING_HABITS: "Eating Habits",
  SELECT_EATING_HABIT: "Select Eating Habit",
  DRINKING_HABITS: "Drinking Habits",
  SELECT_DRINKING_HABIT: "Select Drinking Habit",
  SMOKING_HABIT: "Smoking Habit",
  SELECT_SMOKING_HABIT: "Select Smoking Habit",
  FATHER: "Father",
  MOTHER: "Mother",
  SIBLINGS: "Siblings",
  COUNTRY: "Country",
  STATE: "State",
  CITY: "City",

  // Form Values - Additional
  MALAYALAM: "Malayalam",
  TAMIL: "Tamil",
  ENGLISH: "English",
  NORMAL: "Normal",
  PHYSICALLY_CHALLENGED: "Physically Challenged",
  VEGETARIAN: "Vegetarian",
  NON_VEGETARIAN: "Non-Vegetarian",
  EGGETARIAN: "Eggetarian",
  NEVER: "Never",
  OCCASIONALLY: "Occasionally",
  FREQUENTLY: "Frequently",

  // Placeholders
  FULL_NAME_PLACEHOLDER_TEXT: "Enter your full name (text only)",
  HEIGHT_PLACEHOLDER_TEXT: "Height (max 3 digits)",
  WEIGHT_PLACEHOLDER_TEXT: "Weight (max 3 digits)",
  CITY_PLACEHOLDER: "Enter your city name (text only)",
  FATHER_PLACEHOLDER: "Father's name",
  MOTHER_PLACEHOLDER: "Mother's name",
  SIBLINGS_PLACEHOLDER: "Details about siblings Names",
  CUSTOM_RELIGION_PLACEHOLDER: "Enter your religion (text only)",
  CUSTOM_CASTE_PLACEHOLDER: "Enter your caste (text only)",
  EDUCATION_DETAILS_PLACEHOLDER: "Enter education details (text only)",
  CUSTOM_EMPLOYMENT_PLACEHOLDER: "Enter your employment (text only)",
  CUSTOM_OCCUPATION_PLACEHOLDER: "Enter your occupation (text only)",

  // Helper Texts
  HEIGHT_HELPER: "Enter your height in centimeters (e.g., 175)",
  WEIGHT_HELPER: "Enter your weight in kilograms (e.g., 65)",
  CHARACTER_COUNT: "characters",

  // Labels
  WEIGHT: "Weight (in kg)",
  EDUCATION_DETAILS: "Education Details",
  CUSTOM_RELIGION: "Custom Religion",
  CUSTOM_CASTE: "Custom Caste",
  CUSTOM_EMPLOYMENT: "Custom Employment",
  CUSTOM_OCCUPATION: "Custom Occupation",
};
