import { createNavigationContainerRef } from '@react-navigation/native';

export const navigationRef = createNavigationContainerRef();

export function navigate(name, params) {
  if (navigationRef.isReady()) {
    navigationRef.navigate(name, params);
  }
}

export function navigateToLikedByOthers() {
  if (navigationRef.isReady()) {
    // Navigate to Home screen first, then to LikedByOthers tab
    navigationRef.navigate('Home', {
      screen: 'LikedByOthers'
    });
  }
}
