import { createNavigationContainerRef } from '@react-navigation/native';

export const navigationRef = createNavigationContainerRef();

export function navigate(name, params) {
  if (navigationRef.isReady()) {
    navigationRef.navigate(name, params);
  }
}

export function navigateToLikedByOthers() {
  if (navigationRef.isReady()) {
    console.log('Navigating to LikedByOthers tab...');

    // Get current route to see where we are
    const currentRoute = navigationRef.getCurrentRoute();
    console.log('Current route:', currentRoute?.name);

    // Reset navigation stack and go to Home with LikedByOthers tab
    navigationRef.reset({
      index: 0,
      routes: [
        {
          name: 'Home',
          params: { screen: 'LikedByOthers' }
        }
      ]
    });
  } else {
    console.log('Navigation not ready yet');
  }
}
