import React, { useEffect } from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { useUserInteractions } from '../contexts/UserInteractionsContext';
import { useNavigation } from '@react-navigation/native';
import styles from '../styles/HeaderNotificationStyles';
import messaging from '@react-native-firebase/messaging';
import notifee, { EventType } from '@notifee/react-native';
import { navigateToLikedByOthers } from './NavigationService';


const HeaderNotification = () => {
  const navigation = useNavigation();
  const {
    notificationCount,
    setNotificationCount
  } = useUserInteractions();

  useEffect(() => {
    // Handle foreground messages - just update notification count, don't display notification
    const unsubscribe = messaging().onMessage(async (remoteMessage) => {
      console.log('Foreground FCM message received:', remoteMessage);
      // Just update the notification count, don't display notification
      if (remoteMessage?.data?.type === 'profile_like') {
        setNotificationCount(1);
      }
    });

    // Handle notifee notification clicks (only for manually displayed notifications)
    notifee.onForegroundEvent(async ({ type, detail }) => {
      if (type === EventType.PRESS) {
        console.log('Foreground notification clicked:', JSON.stringify(detail, null, 2));
        if (detail.notification?.data?.screen === 'LikedByOthers' || detail.notification?.data?.type === 'profile_like') {
          console.log('Navigating to LikedByOthers from foreground notification');
          // Use the navigation service for consistent navigation
          navigateToLikedByOthers();
        }
        setNotificationCount(1);
      }
    });

    return unsubscribe;
  }, []);


  return (
    <TouchableOpacity
      style={styles.container}
      onPress={() => {
        navigation.navigate('LikedByOthers')
        setNotificationCount(0)
      }}
    >
      {notificationCount > 0 && (
        <View style={styles.icon}>
          <Text>🔔</Text>
          <View style={styles.badge}>
            <Text style={styles.badgeText}>{notificationCount}</Text>
          </View>
        </View>
      )}
    </TouchableOpacity>
  );
};

export default HeaderNotification;
