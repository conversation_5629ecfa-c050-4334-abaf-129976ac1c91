import React, { useEffect } from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { useUserInteractions } from '../contexts/UserInteractionsContext';
import { useNavigation } from '@react-navigation/native';
import styles from '../styles/HeaderNotificationStyles';
import messaging from '@react-native-firebase/messaging';
import notifee, { EventType } from '@notifee/react-native';
import { navigateToLikedByOthers } from './NavigationService';

const HeaderNotification = () => {
  const navigation = useNavigation();
  const {
    notificationCount,
    setNotificationCount
  } = useUserInteractions();

  useEffect(() => {
    // Handle foreground messages - just update notification count, don't display notification
    const unsubscribe = messaging().onMessage(async (remoteMessage) => {
      console.log('Foreground FCM message received:', remoteMessage);
      // Just update the notification count, don't display notification
      if (remoteMessage?.data?.type === 'profile_like') {
        setNotificationCount(1);
      }
    });

    // Handle notifee notification clicks (only for manually displayed notifications)
    notifee.onForegroundEvent(async ({ type, detail }) => {
      if (type === EventType.PRESS) {
        console.log('Foreground notification clicked:', JSON.stringify(detail, null, 2));
        if (detail.notification?.data?.screen === 'LikedByOthers' || detail.notification?.data?.type === 'profile_like') {
          console.log('Navigating to LikedByOthers from foreground notification');
          // Navigate to LikedByOthers tab
          navigation.navigate('Home', { screen: 'LikedByOthers' });
        }
        setNotificationCount(1);
      }
    });

    return unsubscribe;
  }, []);


  // Test function to manually trigger navigation
  const testNavigation = () => {
    console.log('Testing navigation to LikedByOthers...');
    navigateToLikedByOthers();
  };

  return (
    <View style={{ flexDirection: 'row', alignItems: 'center' }}>
      {/* Test button - remove this after testing */}
      <TouchableOpacity
        style={{ marginRight: 10, padding: 5, backgroundColor: 'red', borderRadius: 5 }}
        onPress={testNavigation}
      >
        <Text style={{ color: 'white', fontSize: 10 }}>TEST</Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={styles.container}
        onPress={() => {
          navigation.navigate('LikedByOthers')
          setNotificationCount(0)
        }}
      >
        {notificationCount > 0 && (
          <View style={styles.icon}>
            <Text>🔔</Text>
            <View style={styles.badge}>
              <Text style={styles.badgeText}>{notificationCount}</Text>
            </View>
          </View>
        )}
      </TouchableOpacity>
    </View>
  );
};

export default HeaderNotification;
