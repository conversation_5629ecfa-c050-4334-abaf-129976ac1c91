import React, { useEffect } from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { useUserInteractions } from '../contexts/UserInteractionsContext';
import { useNavigation } from '@react-navigation/native';
import styles from '../styles/HeaderNotificationStyles';
import messaging from '@react-native-firebase/messaging';

const HeaderNotification = () => {
  const navigation = useNavigation();
  const {
    notificationCount,
    setNotificationCount
  } = useUserInteractions();

  useEffect(() => {
    // Handle foreground messages - just update notification count, don't display notification
    const unsubscribe = messaging().onMessage(async (remoteMessage) => {
      console.log('Foreground FCM message received:', remoteMessage);
      // Just update the notification count, don't create any notification
      // FCM will handle the notification display automatically
      if (remoteMessage?.data?.type === 'profile_like' ||
          remoteMessage?.notification?.body?.includes('liked your profile')) {
        setNotificationCount(1);
      }
    });

    return unsubscribe;
  }, []);


  return (
    <TouchableOpacity
      style={styles.container}
      onPress={() => {
        navigation.navigate('LikedByOthers')
        setNotificationCount(0)
      }}
    >
      {notificationCount > 0 && (
        <View style={styles.icon}>
          <Text>🔔</Text>
          <View style={styles.badge}>
            <Text style={styles.badgeText}>{notificationCount}</Text>
          </View>

        </View>
      )}
    </TouchableOpacity>
  );
};

export default HeaderNotification;
