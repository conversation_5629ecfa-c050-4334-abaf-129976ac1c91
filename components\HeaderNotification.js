import React, { useEffect } from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { useUserInteractions } from '../contexts/UserInteractionsContext';
import { useNavigation } from '@react-navigation/native';
import styles from '../styles/HeaderNotificationStyles';
import messaging from '@react-native-firebase/messaging';
import notifee, { EventType } from '@notifee/react-native';
import { displayNotification } from '../components/NotificationService';

const HeaderNotification = () => {
  const navigation = useNavigation();
  const {
    notificationCount,
    setNotificationCount
  } = useUserInteractions();

  useEffect(() => {
    // Handle foreground messages
    const unsubscribe = messaging().onMessage(async (remoteMessage) => {
      await displayNotification(remoteMessage)
    });

    notifee.onForegroundEvent(async ({ type, detail }) => {
      if (type === EventType.PRESS) {
        console.log('Foreground notification clicked:', detail);
        if (detail.notification?.data?.screen === 'LikedByOthers') {
          // Navigate to LikedByOthers tab
          navigation.navigate('Home', { screen: 'LikedByOthers' });
        }
        setNotificationCount(1);
      }
    });

    return unsubscribe;
  }, []);


  return (
    <TouchableOpacity
      style={styles.container}
      onPress={() => {
        navigation.navigate('LikedByOthers')
        setNotificationCount(0)
      }}
    >
      {notificationCount > 0 && (
        <View style={styles.icon}>
          <Text>🔔</Text>
          <View style={styles.badge}>
            <Text style={styles.badgeText}>{notificationCount}</Text>
          </View>

        </View>
      )}
    </TouchableOpacity>
  );
};

export default HeaderNotification;
