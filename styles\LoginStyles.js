
import { Dimensions, StyleSheet, Platform } from 'react-native'
import { variables } from '../styles/Variables'

const windowHeight = Dimensions.get('window').height;
const windowWidth = Dimensions.get('window').width;

const { themeBGColor, white, gray1, inputTextColor } = variables;

export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: white,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 20,
    paddingVertical: 10,
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 20,
  },
  logo: {
    width: 320,
    resizeMode: 'center',
  },
  separator: {
    marginVertical: 15,
    width: windowWidth * 0.8,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  separatorLine: {
    flex: 1,
    height: 1,
    backgroundColor: '#E0E0E0',
  },
  separatorText: {
    marginHorizontal: 10,
    color: '#888',
    fontSize: 14,
    fontWeight: '500',
  },
  logoTitle: {
    fontSize: 26,
    fontWeight: 'bold',
    color: themeBGColor,
    marginBottom: 5,
    letterSpacing: 1,
    textAlign: 'center',
  },
  logoSubHeading: {
    fontSize: 14,
    color: '#666',
    marginBottom: 15,
    textAlign: 'center',
    fontWeight: '400',
    letterSpacing: 0.3,
  },
  loaderContainer: {
    position: 'absolute',
    top: 0, left: 0, right: 0, bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.4)',
    justifyContent: 'center',
    alignItems: 'center',
    height: windowHeight,
    zIndex: 1000
  },
  newUser: {
    marginTop: 5,
    fontSize: 16,
    color: themeBGColor,
    fontWeight: '600',
  },
  inputError: {
    height: 48,
    borderWidth: 1,
    borderColor: '#FF5252',
    borderRadius: 10,
    paddingHorizontal: 15,
    paddingVertical: 12,
    width: windowWidth * 0.85,
    backgroundColor: '#FFF5F5',
    fontSize: 16,
    color: inputTextColor,
  },
  errorText: {
    color: '#FF5252',
    fontSize: 12,
    marginTop: 4,
    marginBottom: 8,
    alignSelf: 'flex-start',
    marginLeft: 4,
  },
  inputContainer: {
    width: windowWidth * 0.85,
    marginBottom: 8,
  },
  inputLabel: {
    fontSize: 14,
    color: inputTextColor,
    marginBottom: 6,
    fontWeight: '500',
    alignSelf: 'flex-start',
    marginLeft: 4,
  },
  input: {
    height: 48,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 10,
    paddingHorizontal: 15,
    paddingVertical: 12,
    width: windowWidth * 0.85,
    backgroundColor: '#FAFAFA',
    fontSize: 16,
    color: inputTextColor,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.08,
        shadowRadius: 3,
      },
      android: {
        elevation: 2,
      },
    }),
  },
  apploginButtonText: {
    fontSize: 16,
    alignSelf: "center",
    color: white,
    fontWeight: '600',
    letterSpacing: 0.5,
  },
  applogin: {
    backgroundColor: themeBGColor,
    paddingVertical: 15,
    paddingHorizontal: 12,
    width: windowWidth * 0.85,
    marginTop: 15,
    marginBottom: 20,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 3 },
        shadowOpacity: 0.15,
        shadowRadius: 5,
      },
      android: {
        elevation: 4,
      },
    }),
  },
  link: {
    marginTop: 15,
    padding: 8,
  },
  linkText: {
    color: '#555',
    fontSize: 14,
    textDecorationLine: 'underline',
  },
  linkRegistered: {
    marginTop: 5,
    padding: 8,
  },
  formContainer: {
    width: '100%',
    alignItems: 'center',
    marginVertical: 10,
  },
  errorPlaceholder: {
    height: 18,
  },
});