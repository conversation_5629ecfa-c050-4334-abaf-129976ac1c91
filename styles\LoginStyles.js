
import { Dimensions, StyleSheet, Platform } from 'react-native'
import { variables } from '../styles/Variables'

const windowHeight = Dimensions.get('window').height;
const windowWidth = Dimensions.get('window').width;

const { themeBGColor, white, gray1, inputTextColor } = variables;

export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: white,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  separator: {
    marginVertical: 20,
    width: windowWidth * 0.8,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  separatorLine: {
    flex: 1,
    height: 1,
    backgroundColor: '#E0E0E0',
  },
  separatorText: {
    marginHorizontal: 10,
    color: '#888',
    fontSize: 14,
    fontWeight: '500',
  },
  logoTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: themeBGColor,
    marginBottom: 10,
    letterSpacing: 1,
    textAlign: 'center',
  },
  logoSubHeading: {
    fontSize: 16,
    color: '#555',
    marginBottom: 30,
    textAlign: 'center',
    fontWeight: '400',
    letterSpacing: 0.5,
  },
  loaderContainer: {
    position: 'absolute',
    top: 0, left: 0, right: 0, bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.4)',
    justifyContent: 'center',
    alignItems: 'center',
    height: windowHeight,
    zIndex: 1000
  },
  newUser: {
    marginTop: 5,
    fontSize: 16,
    color: themeBGColor,
    fontWeight: '600',
  },
  inputError: {
    height: 50,
    borderWidth: 1,
    borderColor: '#FF5252',
    borderRadius: 8,
    padding: 12,
    width: windowWidth * 0.85,
    backgroundColor: '#F9F9F9',
    fontSize: 16,
    color: inputTextColor,
  },
  errorText: {
    color: '#FF5252',
    fontSize: 12,
    marginTop: 4,
    marginBottom: 8,
    alignSelf: 'flex-start',
    marginLeft: 4,
  },
  inputContainer: {
    width: windowWidth * 0.85,
    marginBottom: 5,
  },
  inputLabel: {
    fontSize: 14,
    color: inputTextColor,
    marginBottom: 6,
    fontWeight: '500',
    alignSelf: 'flex-start',
    marginLeft: 4,
  },
  input: {
    height: 50,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 8,
    padding: 12,
    width: windowWidth * 0.85,
    backgroundColor: '#F9F9F9',
    fontSize: 16,
    color: inputTextColor,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
      },
      android: {
        elevation: 1,
      },
    }),
  },
  apploginButtonText: {
    fontSize: 16,
    alignSelf: "center",
    color: white,
    fontWeight: '600',
    letterSpacing: 0.5,
  },
  applogin: {
    backgroundColor: themeBGColor,
    paddingVertical: 14,
    paddingHorizontal: 12,
    width: windowWidth * 0.85,
    marginTop: 20,
    marginBottom: 25,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.2,
        shadowRadius: 4,
      },
      android: {
        elevation: 3,
      },
    }),
  },
  link: {
    marginTop: 15,
    padding: 8,
  },
  linkText: {
    color: '#555',
    fontSize: 14,
    textDecorationLine: 'underline',
  },
  linkRegistered: {
    marginTop: 5,
    padding: 8,
  },
  formContainer: {
    width: '100%',
    alignItems: 'center',
    marginVertical: 20,
  },
  errorPlaceholder: {
    height: 20,
  },
});