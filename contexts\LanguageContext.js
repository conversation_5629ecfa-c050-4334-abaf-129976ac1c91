import React, { createContext, useContext, useState, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { englishTexts } from '../helper/languages/english';
import { malayalamTexts } from '../helper/languages/malayalam';

const LanguageContext = createContext();

export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (!context) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};

export const LanguageProvider = ({ children }) => {
  const [currentLanguage, setCurrentLanguage] = useState('english');
  const [texts, setTexts] = useState(englishTexts);

  // Load saved language preference on app start
  useEffect(() => {
    loadLanguagePreference();
  }, []);

  const loadLanguagePreference = async () => {
    try {
      const savedLanguage = await AsyncStorage.getItem('selectedLanguage');
      if (savedLanguage) {
        setCurrentLanguage(savedLanguage);
        setTexts(savedLanguage === 'malayalam' ? malayalamTexts : englishTexts);
      }
    } catch (error) {
      console.error('Error loading language preference:', error);
    }
  };

  const changeLanguage = async (language) => {
    try {
      setCurrentLanguage(language);
      setTexts(language === 'malayalam' ? malayalamTexts : englishTexts);
      await AsyncStorage.setItem('selectedLanguage', language);
    } catch (error) {
      console.error('Error saving language preference:', error);
    }
  };

  const value = {
    currentLanguage,
    texts,
    changeLanguage,
    isEnglish: currentLanguage === 'english',
    isMalayalam: currentLanguage === 'malayalam'
  };

  return (
    <LanguageContext.Provider value={value}>
      {children}
    </LanguageContext.Provider>
  );
};
