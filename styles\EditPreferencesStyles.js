import { StyleSheet, Dimensions } from 'react-native';

const screenWidth = Dimensions.get('window').width;

const styles = StyleSheet.create({
    container: {
        flexGrow: 1,
        padding: 20,
        backgroundColor: '#fff',
    },
    sliderContainer: {
        alignItems: 'center',
        position:'relative',
        zIndex: -1,
    },
    multiSlider: {
        width: screenWidth - 40,
    },
    markerStyle: {
        height: 20,
        width: 20,
        backgroundColor: '#ff9f00',
        borderRadius: 10,
        borderWidth: 2,
        borderColor: '#fff',
    },
    trackStyle: {
        height: 6,
        backgroundColor: '#ddd',
        borderRadius: 4,
    },
    selectedStyle: {
        backgroundColor: '#ff9f00',
    },
    pickerContainer: {
        marginVertical: 10,
    },
    pickerWrapper: {
        borderWidth: 1,
        borderColor: '#f8f8f8',
        borderRadius: 8,
        backgroundColor: '#f8f8f8',
        overflow: 'hidden',
    },
    picker: {
        height: 50,
        fontSize: 16,
        paddingHorizontal: 10,
        backgroundColor: '#f8f8f8',
    },
    buttonContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        gap: 10,
        marginTop: 20,
    },
    saveButton: {
        backgroundColor: '#007bff',
        padding: 15,
        borderRadius: 8,
        alignItems: 'center',
        borderWidth: 1,
        borderColor: '#007bff',
        flex: 1,
    },
    saveButtonDisabled: {
        backgroundColor: '#cccccc',
        borderColor: '#cccccc',
    },
    saveButtonText: {
        color: 'white',
        fontSize: 16,
        fontWeight: 'bold',
    },
    clearButton: {
        backgroundColor: '#dc3545',
        padding: 15,
        borderRadius: 8,
        alignItems: 'center',
        borderWidth: 1,
        borderColor: '#dc3545',
        flex: 1,
    },
    buttonDisabled: {
        backgroundColor: '#cccccc',
        borderColor: '#cccccc',
    },
    clearButtonText: {
        color: 'white',
        fontSize: 16,
        fontWeight: 'bold',
    },
    label: {
        fontSize: 16,
        fontWeight: "bold",
        marginBottom: 5,
        textTransform: 'capitalize'
    },
    selectedContainer: {
        flexDirection: "row",
        flexWrap: "wrap",
        gap: 8, // Increased gap for better spacing
        marginBottom: 10,
        paddingVertical: 4, // Add some padding for better visual appearance
    },
    badge: {
        backgroundColor: "#4caf50", // Changed to match header and save button
        borderRadius: 15,
        paddingHorizontal: 10,
        paddingVertical: 5,
        marginRight: 8,
        marginBottom: 8,
        position: 'relative',
        paddingRight: 30, // Increased to give more space for the close button
        flexDirection: 'row',
        alignItems: 'center',
    },
    badgeText: {
        color: "#FFFFFF",
        fontSize: 14,
        marginRight: 5, // Add space between text and close button
    },
    placeholder: {
        color: "#888",
        fontSize: 14,
    },
    picker: {
        position: 'relative',
        zIndex:-1,
        borderColor: '#ddd',
    },
    dropDownContainer: {
        marginTop: 0,
    },
    removeButton: {
        width: 24, // Fixed width for better touch target
        height: 24, // Fixed height for better touch target
        alignItems: 'center',
        justifyContent: 'center',
        position: 'absolute',
        right: 4,
        top: 14, // Center vertically
        marginTop: -12, // Half of height to center
        backgroundColor: 'rgba(0,0,0,0.1)', // Slight background for better visibility
        borderRadius: 12, // Make it circular
        zIndex: 10, // Ensure it's above other elements
    },
    removeButtonText: {
        color: '#FFF',
        fontSize: 16,
        fontWeight: 'bold',
        textAlign: 'center',
        lineHeight: 20, // Helps center the 'x'
    },
});

export default styles;
