import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import HomeScreen from './HomeScreen';
import FavouriteScreen from './FavouriteScreen';
import SearchScreen from './SearchScreen';
import ProfileScreen from './ProfileScreen';
import LikedByOthersScreen from './LikedByOthersScreen';
import { variables } from '../styles/Variables';
import HeaderNotification from '../components/HeaderNotification';
import HeaderBalance from '../components/HeaderBalance';
import { useLanguage } from '../contexts/LanguageContext';

const Tab = createBottomTabNavigator();

const HomeTabScreen = ({ navigation, route }) => {
  const { texts } = useLanguage();
  // Get the initial screen from route params
  const initialRouteName = route?.params?.screen || 'HomeTab';

  return (
    <Tab.Navigator
      initialRouteName={initialRouteName}
      screenOptions={({ route }) => ({
        tabBarIcon: ({ color, size }) => {
          let iconName;
          size = 20;

          if (route.name === 'HomeTab') {
            iconName = 'home';
          } else if (route.name === 'Favourite') {
            iconName = 'heart';
          } else if (route.name === 'LikedByOthers') {
            iconName = 'users';
          } else if (route.name === 'Search') {
            iconName = 'search';
          } else if (route.name === 'Profile') {
            iconName = 'user';
          }

          return <FontAwesome name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: variables.themeBGColor,
        tabBarInactiveTintColor: '#333',
        headerStyle: { backgroundColor: variables.themeBGColor },
        headerTintColor: variables.white,
      })}
    >
      <Tab.Screen
        name="HomeTab"
        component={HomeScreen}
        options={{
          title: texts.HOME,
          headerTitle: () => <HeaderBalance />,
          headerRight: () => <HeaderNotification />,
        }}
      />
      <Tab.Screen
        name="Favourite"
        component={FavouriteScreen}
        options={{ title: texts.FAVOURITE }}
      />
      <Tab.Screen
        name="LikedByOthers"
        component={LikedByOthersScreen}
        options={{ title: texts.LIKED_BY_OTHERS }}
      />
      <Tab.Screen
        name="Search"
        component={SearchScreen}
        options={{ title: texts.SEARCH }}
      />
      <Tab.Screen
        name="Profile"
        component={ProfileScreen}
        options={{ title: texts.PROFILE }}
      />
    </Tab.Navigator>
  );
};

export default HomeTabScreen;
