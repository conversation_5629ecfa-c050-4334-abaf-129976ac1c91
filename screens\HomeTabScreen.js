import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import HomeScreen from './HomeScreen';
import FavouriteScreen from './FavouriteScreen';
import SearchScreen from './SearchScreen';
import ProfileScreen from './ProfileScreen';
import LikedByOthersScreen from './LikedByOthersScreen';
import { variables } from '../styles/Variables';
import HeaderNotification from '../components/HeaderNotification';
import HeaderBalance from '../components/HeaderBalance';

const Tab = createBottomTabNavigator();

const HomeTabScreen = ({ navigation, route }) => {
  // Get the initial screen from route params
  const initialRouteName = route?.params?.screen || 'HomeTab';

  return (
    <Tab.Navigator
      initialRouteName={initialRouteName}
      screenOptions={({ route }) => ({
        tabBarIcon: ({ color, size }) => {
          let iconName;
          size = 20;

          if (route.name === 'HomeTab') {
            iconName = 'home';
          } else if (route.name === 'Favourite') {
            iconName = 'heart';
          } else if (route.name === 'LikedByOthers') {
            iconName = 'users';
          } else if (route.name === 'Search') {
            iconName = 'search';
          } else if (route.name === 'Profile') {
            iconName = 'user';
          }

          return <FontAwesome name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: variables.themeBGColor,
        tabBarInactiveTintColor: '#333',
        headerStyle: { backgroundColor: variables.themeBGColor },
        headerTintColor: variables.white,
      })}
    >
      <Tab.Screen
        name="HomeTab"
        component={HomeScreen}
        options={{
          title: 'Home',
          headerTitle: () => <HeaderBalance />,
          headerRight: () => <HeaderNotification />,
        }}
      />
      <Tab.Screen
        name="Favourite"
        component={FavouriteScreen}
        options={{ title: 'Favourite' }}
      />
      <Tab.Screen
        name="LikedByOthers"
        component={LikedByOthersScreen}
        options={{ title: 'Liked By Others' }}
      />
      <Tab.Screen
        name="Search"
        component={SearchScreen}
        options={{ title: 'Search' }}
      />
      <Tab.Screen
        name="Profile"
        component={ProfileScreen}
        options={{ title: 'Profile' }}
      />
    </Tab.Navigator>
  );
};

export default HomeTabScreen;
