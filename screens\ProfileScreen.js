import React, { useState, useEffect } from 'react';
import {
  View,
  Image,
  Text,
  Alert,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  FlatList,
} from 'react-native';
import { launchImageLibrary } from 'react-native-image-picker';
import firestore from '@react-native-firebase/firestore';
import storage from '@react-native-firebase/storage';
import auth from '@react-native-firebase/auth';
import { properties } from '../helper/Property';
import localStyles from '../styles/ProfileStyles';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

const MatrimonialProfile = ({ navigation }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [profileImages, setProfileImages] = useState([null, null, null]);
  const [profileData, setProfileData] = useState({});
  const [uploadingIndex, setUploadingIndex] = useState(null); // Track which image is uploading

  useEffect(() => {
    const unsubscribe = auth().onAuthStateChanged((user) => {
      if (user) {
        setUser(user);
        fetchUserProfile(user.uid);
      } else {
        setUser(null);
      }
    });
    return unsubscribe;
  }, []);

  const fetchUserProfile = async (uid) => {
    try {
      const userProfileDoc = await firestore().collection('users').doc(uid).get();
      if (userProfileDoc.exists) {
        const data = userProfileDoc.data();
        setProfileImages([
          data.profileImage1 || null,
          data.profileImage2 || null,
          data.profileImage3 || null,
        ]);
        setProfileData({
          fullName: data.fullName || '',
          age: data.age || '',
          gender: data.gender || '',
          dateOfBirth: data.dateOfBirth || '',
          email: data.email || '',
          phoneNumber: data.phoneNumber || '',
          userId: data.userId || '',
        });
      }
    } catch (error) {
      console.error('Error fetching profile:', error.message);
      Alert.alert('Error', `Failed to load profile: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const uploadImage = async (index) => {
    const result = await launchImageLibrary({
      mediaType: 'photo',
      quality: 0.8,
      maxWidth: 700,
      maxHeight: 1000
    });

    if (result.didCancel) {
      console.log('User cancelled image picker');
      return;
    }

    if (result.errorCode) {
      console.error('ImagePicker Error: ', result.errorMessage);
      return;
    }

    const image = result.assets[0];
    const { uri } = image;
    if (!uri) return;

    try {
      setUploadingIndex(index); // Show loader for this specific image
      const fileName = `profileImage${index + 1}_${Date.now()}.jpg`;
      const storageRef = storage().ref(`profileImages/${user.uid}/${fileName}`);
      await storageRef.putFile(uri);
      const downloadURL = await storageRef.getDownloadURL();

      const updatedImages = [...profileImages];
      updatedImages[index] = downloadURL;
      setProfileImages(updatedImages);

      const updateData = {};
      updateData[`profileImage${index + 1}`] = downloadURL;
      await firestore().collection('users').doc(user.uid).update(updateData);

    } catch (error) {
      console.error('Error uploading image:', error.message);
      Alert.alert('Failed to upload image: Can you try after sometime');
    } finally {
      setUploadingIndex(null); // Hide loader
    }
  };

  const deleteImage = async (index) => {
    Alert.alert(
      'Delete Image',
      'Are you sure you want to delete this image?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              setUploadingIndex(index); // Show loader while deleting

              // Update the local state
              const updatedImages = [...profileImages];
              updatedImages[index] = null;
              setProfileImages(updatedImages);

              // Update Firestore
              const updateData = {};
              updateData[`profileImage${index + 1}`] = firestore.FieldValue.delete();
              await firestore().collection('users').doc(user.uid).update(updateData);

              // Note: We're not deleting from Firebase Storage to avoid breaking existing references
              // The storage cleanup can be handled separately if needed

            } catch (error) {
              console.error('Error deleting image:', error.message);
              Alert.alert('Error', 'Failed to delete image. Please try again.');

              // Revert the local state on error
              fetchUserProfile(user.uid);
            } finally {
              setUploadingIndex(null); // Hide loader
            }
          },
        },
      ]
    );
  };

  const handleLogout = () => {
    auth()
      .signOut()
      .then(() => navigation.replace(properties.LOGIN))
      .catch(error => Alert.alert('Error', `Logout failed: ${error.message}`));
  };

  // Function to open the image slider when a thumbnail is clicked
  const handleImagePress = (index = 0) => {
    // Filter out null images
    const images = profileImages.filter(img => img !== null);

    // Only navigate if there's at least one image
    if (images.length > 0) {
      // Find the correct index in the filtered array
      let initialIndex = 0;
      if (index > 0) {
        // Count non-null images before this index to determine the correct initialIndex
        const nonNullBeforeIndex = profileImages
          .slice(0, index)
          .filter(img => img !== null)
          .length;
        initialIndex = nonNullBeforeIndex;
      }

      navigation.navigate('ImageSwiperScreen', {
        images,
        initialIndex
      });
    }
  };

  if (loading) {
    return (
      <View style={localStyles.loadingContainer}>
        <ActivityIndicator size="large" color="transparent" />
      </View>
    );
  }

  const renderImageOrUploadButton = (index) => {
    if (uploadingIndex === index) {
      return (
        <View style={index === 0 ? localStyles.profileImage : localStyles.additionalPhotoImage}>
          <ActivityIndicator size="small" color="#333" />
        </View>
      );
    }

    if (profileImages[index] !== null) {
      // If image exists, show the image with edit and delete overlays
      return (
        <View style={{ width: '100%', height: '100%', position: 'relative' }}>
          <Image
            source={{ uri: profileImages[index] }}
            style={index === 0 ? localStyles.profileImage : localStyles.additionalPhotoImage}
          />
          <TouchableOpacity
            style={localStyles.editOverlay}
            onPress={() => uploadImage(index)}
          >
            <Icon name="pencil" size={16} color="#fff" />
          </TouchableOpacity>
          <TouchableOpacity
            style={{ ...localStyles.deleteOverlay, right: 50 }}
            onPress={() => deleteImage(index)}
          >
            <Icon name="delete" size={16} color="#fff" />
          </TouchableOpacity>
        </View>
      );
    }

    // If no image, show the upload button
    return (
      <View style={localStyles.plusContainer}>
        <Text style={localStyles.plusText}>+</Text>
        <Text style={localStyles.plusLabel}>
          {index === 0 ? 'Add Profile Image' : 'Upload Photo'}
        </Text>
      </View>
    );
  };

  // Function to render the thumbnail gallery
  const renderThumbnailGallery = () => {
    // Only show gallery if there's at least one image
    const hasImages = profileImages.some(img => img !== null);

    if (!hasImages) return null;

    // Count how many images we have
    const imageCount = profileImages.filter(img => img !== null).length;

    return (
      <View style={localStyles.thumbnailGalleryContainer}>
        <View style={localStyles.galleryHeader}>
          <Icon name="image-multiple" size={16} color="#007bff" style={{marginRight: 5}} />
          <Text style={localStyles.galleryTitle}>
            My Photos ({imageCount})
          </Text>
        </View>
        <Text style={localStyles.gallerySubtitle}>
          Tap on any photo to view in full screen
        </Text>
        <View style={localStyles.thumbnailRow}>
          {profileImages.map((image, index) => (
            image && (
              <TouchableOpacity
                key={index}
                style={localStyles.thumbnail}
                onPress={() => handleImagePress(index)}
                activeOpacity={0.7}
              >
                <Image
                  source={{ uri: image }}
                  style={localStyles.thumbnailImage}
                />
                <View style={localStyles.viewIconOverlay}>
                  <Icon name="eye" size={14} color="#fff" />
                </View>
              </TouchableOpacity>
            )
          ))}
        </View>
      </View>
    );
  };

  return (
    <ScrollView contentContainerStyle={localStyles.scrollContainer}>
      <View style={localStyles.header}>
        <View style={localStyles.uploadSectionTitle}>
          <Icon name="camera" size={16} color="#007bff" style={{marginRight: 5}} />
          <Text style={localStyles.uploadTitle}>Profile Photo</Text>
        </View>
        <TouchableOpacity
          onPress={() => uploadImage(0)}
          style={localStyles.additionalPhotoMain}
          disabled={uploadingIndex !== null} // Disable while any upload is in progress
          activeOpacity={0.8}
        >
          {renderImageOrUploadButton(0)}
        </TouchableOpacity>
      </View>

      <View style={localStyles.additionalPhotosContainer}>
        <TouchableOpacity
          onPress={() => uploadImage(1)}
          style={localStyles.additionalPhoto}
          disabled={uploadingIndex !== null}
          activeOpacity={0.8}
        >
          {renderImageOrUploadButton(1)}
        </TouchableOpacity>
        <TouchableOpacity
          onPress={() => uploadImage(2)}
          style={localStyles.additionalPhoto}
          disabled={uploadingIndex !== null}
          activeOpacity={0.8}
        >
          {renderImageOrUploadButton(2)}
        </TouchableOpacity>
      </View>

      <View style={localStyles.sectionLabel}>
        <Text style={localStyles.sectionLabelText}>Upload up to 3 profile photos</Text>
      </View>

      {/* Thumbnail Gallery - clearly separated */}
      <View style={localStyles.sectionDivider} />

      {renderThumbnailGallery()}

      <View style={localStyles.profileInfoCard}>
        <Text style={localStyles.name}>
          {profileData.fullName}
        </Text>
        <Text style={localStyles.subInfo}>User ID: {profileData.userId}</Text>
        <Text style={localStyles.subInfo}>Email: {profileData.email}</Text>
        <Text style={localStyles.subInfo}>Phone: {profileData.phoneNumber}</Text>
      </View>

      <View style={localStyles.actionsContainer}>
        <TouchableOpacity
          style={localStyles.actionButton}
          onPress={() => navigation.navigate('EditProfile')}>
          <Icon name="account-edit" size={18} color="#007bff" />
          <Text style={localStyles.actionButtonText}>Edit Profile</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={localStyles.actionButton}
          onPress={() => navigation.navigate('EditPreferences')}>
          <Icon name="tune" size={18} color="#007bff" />
          <Text style={localStyles.actionButtonText}>Edit Preferences</Text>
        </TouchableOpacity>
      </View>

      <TouchableOpacity onPress={handleLogout} style={localStyles.logoutButton}>
        <Text style={localStyles.logoutButtonText}>Logout</Text>
      </TouchableOpacity>
    </ScrollView>
  );
};

export default MatrimonialProfile;