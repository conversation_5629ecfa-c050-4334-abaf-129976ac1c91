import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import HomeScreen from '../screens/HomeTabScreen';
import LikedByOthersScreen from '../screens/LikedByOthersScreen';
import LoginScreen from '../screens/LoginScreen';
import RegisterScreen from '../screens/RegisterScreen';
import UserListScreen from '../screens/UserListScreen';
import ImageSwiperScreen from '../screens/ImageSwiperScreen';
import ProfileDetailScreen from '../screens/ProfileDetailScreen';
import EditPreferences from '../screens/EditPreferences';
import EditProfile from '../screens/EditProfile';
import { properties } from '../helper/Property';
import { config } from '../helper/Config';
import { navigationRef } from './NavigationService';
import { useLanguage } from '../contexts/LanguageContext';

const Stack = createStackNavigator();

const AppNavigation = () => {
    const { texts } = useLanguage();

    return (
        <NavigationContainer ref={navigationRef} independent={true}>
            <Stack.Navigator screenOptions={config.globalScreenOptions}>
                <Stack.Screen name={properties.LOGIN} component={LoginScreen} options={{ headerShown: false }} />
                <Stack.Screen name={properties.REGISTER} component={RegisterScreen} options={{ title: texts.LETS_REGISTER }} />
                <Stack.Screen name={properties.HOME} component={HomeScreen} options={{ headerShown: false }} />
                <Stack.Screen name="UserListScreen" component={UserListScreen} options={{ title: 'All Users' }} />
                <Stack.Screen name="ProfileDetailScreen" component={ProfileDetailScreen} options={{ title: texts.PROFILE }} />
                <Stack.Screen name="ImageSwiperScreen" component={ImageSwiperScreen} options={{ title: 'Profile images' }} />
                <Stack.Screen name="EditProfile" component={EditProfile} options={{ title: texts.EDIT_PROFILE }} />
                <Stack.Screen name="EditPreferences" component={EditPreferences} options={{ title: texts.EDIT_PREFERENCES }} />
                <Stack.Screen name="LikedByOthers" component={LikedByOthersScreen} />
            </Stack.Navigator>
        </NavigationContainer>
    );
};

export default AppNavigation;
