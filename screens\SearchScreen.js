import React, { useState, useEffect, useCallback } from 'react';
import {
  View, Text, TextInput, TouchableOpacity, FlatList,
  Alert, ActivityIndicator, KeyboardAvoidingView, Dimensions, ScrollView
} from 'react-native';
import MultiSlider from '@ptomasroos/react-native-multi-slider';
import firestore from '@react-native-firebase/firestore';
import auth from '@react-native-firebase/auth';
import DropDownPicker from 'react-native-dropdown-picker';
import Icon from 'react-native-vector-icons/Ionicons'; // Import Ionicons for the close icon
import styles from '../styles/SearchScreenStyles';
import UserCard from '../components/UserCard';
import formValues from '../helper/FormValues';
import { useUserInteractions } from '../contexts/UserInteractionsContext';
import useScreenFocus from '../helper/useScreenFocus';

const { width } = Dimensions.get('window');

const MatchesScreen = () => {
  const PAGE_SIZE = 10; // Define page size constant
  const [searchText, setSearchText] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [loading, setLoading] = useState(false);
  const [advancedSearchVisible, setAdvancedSearchVisible] = useState(false);
  const { currentUsergender, viewedProfileIds, notInteresteds, refreshUserData } = useUserInteractions();

  // State for pagination
  const [lastDoc, setLastDoc] = useState(null);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [allUsersLoaded, setAllUsersLoaded] = useState(false);

  // Create a memoized callback for refreshing data
  const handleRefresh = useCallback(() => {
    refreshUserData();
  }, [refreshUserData]);

  // Refresh data when the screen comes into focus
  useScreenFocus(handleRefresh);

  // State for saved searches
  const [savedSearches, setSavedSearches] = useState([]);

  // Get all form values from the centralized helper
  let {
    maritalStatusValues,
    districtValues,
    highestEducationValues,
    religionValues,
    employedInValues,
    salaryValues,
    starValues,
    casteData
  } = formValues;

  // Remove "Other" option from all dropdown values
  const removeOther = (array) => array.filter(item => item !== 'Other');

  // Add "Any" option to all dropdown values (without "Other")
  maritalStatusValues = ['Any', ...removeOther(maritalStatusValues)];
  districtValues = ['Any', ...removeOther(districtValues)];
  highestEducationValues = ['Any', ...removeOther(highestEducationValues)];
  religionValues = ['Any', ...removeOther(religionValues)];
  employedInValues = ['Any', ...removeOther(employedInValues)];
  salaryValues = ['Any', ...removeOther(salaryValues)];
  starValues = ['Any', ...removeOther(starValues)];

  // Dropdown states for all filters
  const [openDropdowns, setOpenDropdowns] = useState({});
  const [filterValues, setFilterValues] = useState({
    education: [],
    religion: [],
    caste: [],
    maritalStatus: [],
    employedIn: [],
    star: [],
    district: []
  });

  // Range states for age and height
  const [age, setAgeRange] = useState([24, 30]);
  const [height, setHeightRange] = useState([150, 180]);

  // State to track if a saved search is being applied
  const [isApplyingSavedSearch, setIsApplyingSavedSearch] = useState(false);

  // Get current user ID
  const currentUserId = auth().currentUser?.uid;

  // Transform array into { label, value } format
  const transformItems = (items) => items.map((item) => ({ label: item, value: item }));

  // Create dropdown options for all filters
  const dropdownOptions = {
    education: transformItems(highestEducationValues),
    religion: transformItems(religionValues),
    maritalStatus: transformItems(maritalStatusValues),
    employedIn: transformItems(employedInValues),
    star: transformItems(starValues),
    district: transformItems(districtValues)
  };

  // Dynamic caste options based on selected religion
  const [casteOptions, setCasteOptions] = useState([]);

  // Update caste options and reset star filter when religion changes
  useEffect(() => {
    if (filterValues.religion && filterValues.religion.length > 0) {
      // Handle multiple religion selections
      let allCasteOptions = [];

      // Check if Hindu is included in the religion selection
      const includesHindu = filterValues.religion.includes('Hindu');

      // If Hindu is not included, reset the star filter
      if (!includesHindu && filterValues.star && filterValues.star.length > 0) {
        setFilterValues(prev => ({
          ...prev,
          star: []
        }));
      }

      // Check if any religion with castes is selected
      const hasReligionsWithCastes = filterValues.religion.some(rel =>
        rel !== 'Any' &&
        rel !== 'Inter-Religion' &&
        rel !== 'No Religion' &&
        rel !== 'Jain' &&
        rel !== 'Jewish' &&
        rel !== 'Other'
      );

      // If no religions with castes are selected, clear caste values
      if (!hasReligionsWithCastes && filterValues.caste && filterValues.caste.length > 0) {
        setFilterValues(prev => ({
          ...prev,
          caste: []
        }));
      }

      filterValues.religion.forEach(religion => {
        // Skip if religion is 'Any' or one of the religions without castes
        if (religion === 'Any' ||
          religion === 'Inter-Religion' ||
          religion === 'No Religion' ||
          religion === 'Jain' ||
          religion === 'Jewish' ||
          religion === 'Other') {
          return;
        }

        // Add castes for this religion
        if (casteData[religion]) {
          allCasteOptions = [...allCasteOptions, ...casteData[religion]];
        }
      });

      // Remove duplicates and transform to dropdown format
      const uniqueCastes = [...new Set(allCasteOptions)];
      setCasteOptions(transformItems(uniqueCastes));
    } else {
      // If no religion is selected or 'Any' is selected
      setCasteOptions([]);

      // Reset star filter and caste values when no religion is selected
      if (filterValues.star && filterValues.star.length > 0) {
        setFilterValues(prev => ({
          ...prev,
          star: []
        }));
      }

      // Also clear caste values
      if (filterValues.caste && filterValues.caste.length > 0) {
        setFilterValues(prev => ({
          ...prev,
          caste: []
        }));
      }
    }
  }, [filterValues.religion]);

  // Fetch saved searches from Firebase on component mount
  useEffect(() => {
    if (currentUserId) {
      const fetchSavedSearches = async () => {
        const userDoc = await firestore().collection('users').doc(currentUserId).get();
        if (userDoc.exists) {
          const userData = userDoc.data();
          if (userData.savedSearches) {
            setSavedSearches(userData.savedSearches);
          }
        }
      };
      fetchSavedSearches();
    }
  }, [currentUserId]);

  // Save current search configuration
  const saveSearch = async () => {
    if (savedSearches.length >= 2) {
      Alert.alert("Info", "You can only save up to 2 searches.");
      return;
    }

    const newSearch = {
      ...filterValues,
      age: [...age],
      height: [...height],
    };

    const updatedSearches = [...savedSearches, newSearch];
    setSavedSearches(updatedSearches);

    // Save to Firebase
    if (currentUserId) {
      await firestore().collection('users').doc(currentUserId).update({
        savedSearches: updatedSearches,
      });
    }
  };

  // Remove a saved search
  const removeSavedSearch = async (index) => {
    const updatedSearches = savedSearches.filter((_, i) => i !== index);
    setSavedSearches(updatedSearches);

    // Update Firebase
    if (currentUserId) {
      await firestore().collection('users').doc(currentUserId).update({
        savedSearches: updatedSearches,
      });
    }
  };

  // Apply a saved search
  const applySavedSearch = (search) => {
    setIsApplyingSavedSearch(true);
    setFilterValues({
      education: search.education || [],
      religion: search.religion || [],
      caste: search.caste || [],
      maritalStatus: search.maritalStatus || [],
      employedIn: search.employedIn || [],
      star: search.star || [],
      district: search.district || []
    });
    setAgeRange(search.age || [24, 30]);
    setHeightRange(search.height || [150, 180]);
  };

  // Trigger search after state updates are complete
  useEffect(() => {
    if (isApplyingSavedSearch) {
      handleSearch();
      setIsApplyingSavedSearch(false); // Reset the flag
    }
  }, [filterValues, age, height, isApplyingSavedSearch]);

  const clearFilters = () => {
    setFilterValues({
      education: [],
      religion: [],
      caste: [],
      maritalStatus: [],
      employedIn: [],
      star: [],
      district: []
    });
    setAgeRange([24, 30]);
    setHeightRange([150, 180]);
    setSearchText('');
    setSearchResults([]);

    // Reset pagination state
    setLastDoc(null);
    setAllUsersLoaded(false);
  };

  const clearSearch = () => {
    setSearchText('');
    setSearchResults([]);

    // Reset pagination state
    setLastDoc(null);
    setAllUsersLoaded(false);
  };

  // Remove a selected value from a filter
  const removeSelectedValue = (key, value) => {
    // If removing Hindu from religion, also clear caste values
    if (key === 'religion' && value === 'Hindu') {
      // Check if Hindu is the only religion with castes
      const otherReligionsWithCastes = filterValues.religion.filter(rel =>
        rel !== 'Hindu' &&
        rel !== 'Any' &&
        rel !== 'Inter-Religion' &&
        rel !== 'No Religion' &&
        rel !== 'Jain' &&
        rel !== 'Jewish' &&
        rel !== 'Other'
      );

      // If no other religions with castes remain, clear caste values
      if (otherReligionsWithCastes.length === 0) {
        setFilterValues(prev => ({
          ...prev,
          [key]: prev[key].filter(item => item !== value),
          caste: [] // Clear caste values
        }));
        return;
      }
    }

    // Normal case - just remove the selected value
    setFilterValues(prev => ({
      ...prev,
      [key]: prev[key].filter(item => item !== value)
    }));
  };

  const searchUsers = async (searchParams, lastDocument = null) => {
    try {
      let query = firestore().collection('users');

      const { text, filters, ageRange, heightRange, gender } = searchParams;
      const isSearchEmpty = text.trim() === '';
      const isFiltersEmpty = Object.values(filters).every(arr => arr.length === 0) &&
        ageRange[0] === 24 && ageRange[1] === 30 &&
        heightRange[0] === 150 && heightRange[1] === 180;

      if (isSearchEmpty && isFiltersEmpty) {
        return { results: [], lastDoc: null };
      }

      // Always filter by opposite gender
      const oppositeGender = gender === 'male' ? 'female' : 'male';
      query = query.where('gender', '==', oppositeGender);

      // Search by user ID or name
      if (text.trim()) {
        const userSnapshot = await query.where('userId', '==', text).get();
        if (!userSnapshot.empty) {
          const results = userSnapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }));
          return { results, lastDoc: null }; // No pagination for direct ID search
        }

        // Search by name (case-insensitive)
        // When searching by name, we'll only apply age/height filters client-side
        // to avoid the Firestore limitation on inequality filters
        let nameQuery = query
          .where('fullName', '>=', text.toLowerCase())
          .where('fullName', '<=', text.toLowerCase() + '\uf8ff')
          .orderBy('fullName'); // Important: Add orderBy when using range filters with pagination

        // Apply pagination if we have a last document
        if (lastDocument) {
          nameQuery = nameQuery.startAfter(lastDocument);
        }

        // Add limit to the query
        nameQuery = nameQuery.limit(PAGE_SIZE);

        // Execute the query
        const snapshot = await nameQuery.get();

        if (!snapshot.empty) {
          let results = snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }));
          const lastDoc = snapshot.docs[snapshot.docs.length - 1];
          return { results, lastDoc };
        }

        return { results: [], lastDoc: null };
      } else {
        // Apply filters - only add where clauses for non-empty filters
        // Note: Firestore has a limit of 10 'in' or '==' clauses per query
        // We'll prioritize the most important filters

        // First, apply the most common filters directly in the query
        let filterCount = 1; // Start at 1 because we already used one for gender

        // We're handling notInteresteds client-side now, so no need to increment filterCount
        const applyFilter = (field, values) => {
          if (values.length > 0 && !values.includes('Any') && filterCount < 9) {
            // Skip filter if length is 1 and the only value is "Other"
            if (field !== 'employedIn' && values.length === 1 && values[0] === 'Other') {
              return false;
            }

            query = query.where(field, 'in', values);
            filterCount++;
            return true;
          }
          return false;
        };

        // Apply filters in order of importance
        const appliedReligion = filters.religion.includes('No Religion')
          ? false
          : applyFilter('religion', filters.religion);
        const appliedMaritalStatus = applyFilter('maritalStatus', filters.maritalStatus);
        const appliedEmployedIn = applyFilter('employedIn', filters.employedIn);
        const appliedCaste = applyFilter('caste', filters.caste);

        // Only apply star filter if religion includes Hindu
        const appliedStar = (filters.religion && filters.religion.includes('Hindu'))
          ? applyFilter('star', filters.star)
          : false;

        const appliedDistrict = applyFilter('district', filters.district);

        // Apply range filters (these don't count toward the 'in' clause limit)
        // Important: When using multiple inequality filters, we can only orderBy one field
        // So we'll use only one inequality filter in the query and handle the other client-side

        // Apply age range filter with orderBy (server-side)
        query = query.where('age', '>=', ageRange[0]).where('age', '<=', ageRange[1]);
        query = query.orderBy('age');

        // We'll apply height filter client-side to avoid the Firestore limitation

        // Apply pagination if we have a last document
        if (lastDocument) {
          query = query.startAfter(lastDocument);
        }

        // Add limit to the query
        query = query.limit(PAGE_SIZE);

        // Get results with pagination
        const snapshot = await query.get();

        if (snapshot.empty) {
          return { results: [], lastDoc: null };
        }

        // Get the last visible document for pagination
        const lastDoc = snapshot.docs[snapshot.docs.length - 1];

        let results = snapshot.docs.map((doc) => ({ userId: doc.id, ...doc.data() }));

        // Filter out users of the same gender (redundant now but kept as a safety check)
        let filteredResults = results.filter((user) => user.gender !== gender);

        // Apply height filter client-side since we can't have two inequality filters in Firestore
        filteredResults = filteredResults.filter(user =>
          user.height >= heightRange[0] && user.height <= heightRange[1]
        );

        // Apply only the filters that weren't applied server-side

        // Only apply caste filter if it wasn't applied server-side
        if (!appliedCaste && filters.caste.length > 0 && !filters.caste.includes('Any') &&
          !(filters.caste.length === 1 && filters.caste[0] === 'Other')) {
          filteredResults = filteredResults.filter(user => filters.caste.includes(user.caste));
        }

        // Only apply these filters if they weren't applied server-side
        if (!appliedReligion && filters.religion.length > 0 &&
          !filters.religion.includes('Any') &&
          !filters.religion.includes('No Religion') &&
          !(filters.religion.length === 1 && filters.religion[0] === 'Other')) {
          filteredResults = filteredResults.filter(user => filters.religion.includes(user.religion));
        }

        if (!appliedMaritalStatus && filters.maritalStatus.length > 0 &&
          !filters.maritalStatus.includes('Any') &&
          !filters.maritalStatus.includes('Other')) {
          // Trim maritalStatus values for comparison
          const trimmedMaritalStatusFilters = filters.maritalStatus.map(item => item.trim());
          filteredResults = filteredResults.filter(user => trimmedMaritalStatusFilters.includes(user.maritalStatus));
        }

        // Always apply education filter client-side (removed from server-side)
        if (filters.education.length > 0 &&
          !filters.education.includes('Any') &&
          !filters.education.includes('Other')) {
          // Trim education values for comparison
          const trimmedEducationFilters = filters.education.map(item => item.trim());
          filteredResults = filteredResults.filter(user => trimmedEducationFilters.includes(user.highestEducation));
        }

        if (!appliedEmployedIn && filters.employedIn.length > 0 &&
          !filters.employedIn.includes('Any')) {
          // employedIn doesn't have an "Other" value
          filteredResults = filteredResults.filter(user => filters.employedIn.includes(user.employedIn));
        }

        // Only apply star filter if religion is Hindu
        if (!appliedStar && filters.star.length > 0 && !filters.star.includes('Any') &&
          filters.religion && filters.religion.includes('Hindu') &&
          !(filters.star.length === 1 && filters.star[0] === 'Other')) {
          filteredResults = filteredResults.filter(user =>
            // Only filter Hindu users by star
            user.religion !== 'Hindu' || filters.star.includes(user.star)
          );
        }

        if (!appliedDistrict && filters.district.length > 0 &&
          !filters.district.includes('Any') &&
          !(filters.district.length === 1 && filters.district[0] === 'Other')) {
          filteredResults = filteredResults.filter(user => filters.district.includes(user.district));

        }

        return { results: filteredResults, lastDoc };
      }
    } catch (error) {
      console.error('Error searching users:', error);
      // Log more detailed error information for debugging
      if (error.code) {
        console.error('Error code:', error.code);
      }
      if (error.message) {
        console.error('Error message:', error.message);
      }

      // Just log the raw error without additional guidance
      console.error('Raw Firebase error:', error);
      return { results: [], lastDoc: null };
    }
  };

  const handleSearch = useCallback(async () => {
    // Reset pagination state
    setLastDoc(null);
    setAllUsersLoaded(false);
    setLoading(true);
    setAdvancedSearchVisible(false);

    try {
      const searchParams = {
        text: searchText,
        filters: filterValues,
        ageRange: age,
        heightRange: height,
        gender: currentUsergender
      };

      console.log('Starting search with params:', {
        text: searchText,
        ageRange: age,
        heightRange: height,
        filterCount: Object.values(filterValues).flat().length
      });

      const { results, lastDoc } = await searchUsers(searchParams);

      // Apply notInteresteds filter
      let filteredResults = results;
      if (notInteresteds && notInteresteds.length > 0) {
        filteredResults = filteredResults.filter(user => {
          const userId = user.userId || user.id;
          return !notInteresteds.includes(userId);
        });
      }

      // Sort to show unviewed profiles first
      if (viewedProfileIds && viewedProfileIds.length > 0) {
        filteredResults = filteredResults.sort((a, b) => {
          const aId = a.userId || a.id;
          const bId = b.userId || b.id;
          const aViewed = viewedProfileIds.includes(aId);
          const bViewed = viewedProfileIds.includes(bId);
          return aViewed - bViewed;
        });
      }

      console.log(`Search completed: Found ${filteredResults.length} results`);

      setSearchResults(filteredResults);
      setLastDoc(lastDoc);
      setAllUsersLoaded(!lastDoc || results.length < PAGE_SIZE);
    } catch (error) {
      console.error('Error in handleSearch:', error);

      // Log more detailed error information for debugging
      if (error.code) {
        console.error('Error code:', error.code);
      }
      if (error.message) {
        console.error('Error message:', error.message);
      }

      // Show the exact Firebase error message
      Alert.alert(
        "Firebase Error",
        error.message || "Unknown error occurred",
        [{ text: "OK" }]
      );
    } finally {
      setLoading(false);
    }
  }, [searchText, filterValues, currentUsergender, age, height, notInteresteds, viewedProfileIds, PAGE_SIZE]);

  // Load more users when scrolling
  const loadMoreUsers = useCallback(async () => {
    if (isLoadingMore || allUsersLoaded || !lastDoc) return;

    setIsLoadingMore(true);

    try {
      const searchParams = {
        text: searchText,
        filters: filterValues,
        ageRange: age,
        heightRange: height,
        gender: currentUsergender
      };

      // Try to fetch more results with pagination
      try {
        console.log('Loading more users with pagination...');
        console.log('Last document ID:', lastDoc.id);

        const { results, lastDoc: newLastDoc } = await searchUsers(searchParams, lastDoc);

        // Apply notInteresteds filter
        let filteredResults = results;
        if (notInteresteds && notInteresteds.length > 0) {
          filteredResults = filteredResults.filter(user => {
            const userId = user.userId || user.id;
            return !notInteresteds.includes(userId);
          });
        }

        if (filteredResults.length > 0) {
          // Add new users, ensuring no duplicates
          setSearchResults(prevResults => {
            const existingIds = new Set(prevResults.map(user => user.userId || user.id));
            const newUsers = filteredResults.filter(user => !existingIds.has(user.userId || user.id));
            return [...prevResults, ...newUsers];
          });

          setLastDoc(newLastDoc);
          setAllUsersLoaded(!newLastDoc || results.length < PAGE_SIZE);
        } else {
          setAllUsersLoaded(true);
        }
      } catch (paginationError) {
        // If pagination fails (likely due to missing index), fall back to a simpler approach
        console.error('Pagination error, falling back to simpler approach:', paginationError);

        // Just log the raw error without additional guidance
        console.error('Raw Firebase error:', paginationError);

        // Mark as all loaded to prevent further pagination attempts
        setAllUsersLoaded(true);

        // Show the exact Firebase error message
        Alert.alert(
          "Firebase Error",
          paginationError.message || "Unknown error occurred",
          [{ text: "OK" }]
        );
      }
    } catch (error) {
      console.error('Error loading more users:', error);
      // Log more detailed error information for debugging
      if (error.code) {
        console.error('Error code:', error.code);
      }
      if (error.message) {
        console.error('Error message:', error.message);
      }

      // Just log the raw error without additional guidance
      console.error('Raw Firebase error:', error);

      // Set allUsersLoaded to true to prevent further attempts that might cause errors
      setAllUsersLoaded(true);

      // Show the exact Firebase error message
      Alert.alert(
        "Firebase Error",
        error.message || "Unknown error occurred",
        [{ text: "OK" }]
      );
    } finally {
      setIsLoadingMore(false);
    }
  }, [searchText, filterValues, currentUsergender, age, height, lastDoc, isLoadingMore, allUsersLoaded, notInteresteds, PAGE_SIZE]);

  // Render selected items for a filter
  const renderSelectedItems = (key) => (
    <View style={styles.selectedItemsContainer}>
      {filterValues[key]
        .filter(item => item !== 'Other') // Filter out "Other" from display
        .map((item) => (
        <View key={item} style={styles.selectedItemBadge}>
          <Text style={styles.selectedItemText}>{item}</Text>
          <TouchableOpacity
            onPress={() => removeSelectedValue(key, item)}
            style={styles.removeItemButton}
            activeOpacity={0.7} // Better feedback when pressed
          >
            <Icon name="close" size={16} color="#FFFFFF" />
          </TouchableOpacity>
        </View>
      ))}
    </View>
  );

  // Render saved searches
  const renderSavedSearches = () => (
    <View style={savedSearches.length > 0 ? styles.savedSearchesContainer : null}>
      {savedSearches.length > 0 ? <Text style={styles.savedSearchesTitle}>Saved Searches</Text> : null}
      {savedSearches.map((search, index) => (
        <View key={index} style={styles.savedSearchItem}>
          <ScrollView style={styles.savedSearchScrollView} showsVerticalScrollIndicator={false}>
            <Text style={styles.savedSearchText}>
              {search.religion?.length > 0 ? `Religion: ${search.religion.join(', ')}\n` : ''}
              {search.caste?.length > 0 ? `Caste: ${search.caste.join(', ')}\n` : ''}
              {search.maritalStatus?.length > 0 ? `Marital Status: ${search.maritalStatus.join(', ')}\n` : ''}
              {search.education?.length > 0 ? `Education: ${search.education.join(', ')}\n` : ''}
              {search.employedIn?.length > 0 ? `Employed In: ${search.employedIn.join(', ')}\n` : ''}
              {/* Only show Star if Religion includes Hindu */}
              {search.religion?.includes('Hindu') && search.star?.length > 0 ? `Star: ${search.star.join(', ')}\n` : ''}
              {search.district?.length > 0 ? `District: ${search.district.join(', ')}\n` : ''}
              {search.age?.[0] ? `Age: ${search.age[0]} - ${search.age[1]}\n` : ''}
              {search.height?.[0] ? `Height: ${search.height[0]} - ${search.height[1]} cm` : ''}
            </Text>
          </ScrollView>
          <View style={styles.savedSearchActions}>
            <TouchableOpacity
              style={styles.searchButtonSmall}
              onPress={() => applySavedSearch(search)}
            >
              <Text style={styles.searchButtonSmallText}>Search</Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => removeSavedSearch(index)}
              style={styles.removeSavedSearchButton}
            >
              <Text style={styles.removeSavedSearchText}>✕</Text>
            </TouchableOpacity>
          </View>
        </View>
      ))}
    </View>
  );

  // Render dropdown for a filter
  const renderDropdown = (key, label) => {
    if (key === 'caste') {
      // Don't show caste dropdown if no religion is selected or if selected religions don't have castes
      const shouldShowCaste = filterValues.religion &&
        filterValues.religion.length > 0 &&
        !filterValues.religion.every(rel =>
          rel === 'Any' ||
          rel === 'Other' ||
          rel === 'Jain' ||
          rel === 'Jewish' ||
          rel === 'Inter-Religion' ||
          rel === 'No Religion');

      if (!shouldShowCaste) {
        return null;
      }

      return (
        <View style={styles.filterSection}>
          <Text style={styles.filterLabel}>{label}</Text>
          {renderSelectedItems(key)}
          <DropDownPicker
            open={openDropdowns[key] || false}
            value={[]}
            items={casteOptions}
            setOpen={(isOpen) => {
              setOpenDropdowns({});
              setOpenDropdowns(prev => ({ ...prev, [key]: isOpen }));
            }}
            setValue={(callback) => {
              const newValues = callback([]);

              setFilterValues(prev => ({
                ...prev,
                [key]: newValues
              }));

              // Close the dropdown
              setOpenDropdowns(prev => ({
                ...prev,
                [key]: false
              }));
            }}
            multiple={true}
            style={styles.dropdown}
            dropDownContainerStyle={styles.dropdownContainer}
            placeholder={`Select ${label}`}
            listMode="SCROLLVIEW"
          />
        </View>
      );
    }

    // For all other dropdowns
    return (
      <View style={styles.filterSection}>
        <Text style={styles.filterLabel}>{label}</Text>
        {renderSelectedItems(key)}
        <DropDownPicker
          open={openDropdowns[key] || false}
          value={[]}
          items={dropdownOptions[key] || []}
          setOpen={(isOpen) => {
            setOpenDropdowns({});
            setOpenDropdowns(prev => ({ ...prev, [key]: isOpen }));
          }}
          setValue={(callback) => {
            const newValues = callback([]);

            setFilterValues(prev => {
              const currentValues = prev[key] || [];
              let updatedValues = [];

              if (newValues.includes('Any')) {
                // If 'Any' is selected, keep only 'Any'
                updatedValues = ['Any'];
              } else {
                // Remove 'Any' if it was previously selected
                updatedValues = [
                  ...new Set([
                    ...currentValues.filter(val => val !== 'Any'),
                    ...newValues
                  ])
                ];
              }

              return {
                ...prev,
                [key]: updatedValues
              };
            });

            // Close the dropdown
            setOpenDropdowns(prev => ({
              ...prev,
              [key]: false
            }));
          }}
          multiple={true}
          style={styles.dropdown}
          dropDownContainerStyle={styles.dropdownContainer}
          placeholder={`Select ${label}`}
          listMode="SCROLLVIEW"
        />
      </View>
    );
  };

  const renderItem = () => (
    <KeyboardAvoidingView>
      <View style={styles.searchContainer}>
        <TextInput
          style={styles.input}
          placeholder="Search by User ID or Name"
          value={searchText}
          onChangeText={text => {
            setSearchText(text);
          }}
          placeholderTextColor="#999"
        />
        {searchText.length > 0 && (
          <TouchableOpacity onPress={clearSearch} style={styles.clearSearchButton}>
            <Text style={styles.clearSearchButtonText}>✕</Text>
          </TouchableOpacity>
        )}
        <TouchableOpacity style={styles.searchButtonTop} onPress={handleSearch}>
          <Text style={styles.buttonText}>Search</Text>
        </TouchableOpacity>
      </View>

      <TouchableOpacity
        style={styles.advancedToggle}
        onPress={() => {
          setAdvancedSearchVisible(!advancedSearchVisible);
        }}
      >
        <Text style={styles.toggleText}>
          {advancedSearchVisible ? '▲ Hide Filters' : '▼ Advanced Filters'}
        </Text>
      </TouchableOpacity>

      {advancedSearchVisible && (
        <View style={styles.advancedSearchContainer}>
          {renderSavedSearches()}

          {/* Age Range Selector */}
          <Text style={styles.filterLabel}>
            Age Range: {age[0]} - {age[1]} years
          </Text>
          <MultiSlider
            values={age}
            min={18}
            max={60}
            step={1}
            onValuesChange={setAgeRange}
            sliderLength={width - 65}
            snapped
            markerStyle={styles.markerStyle}
            trackStyle={styles.trackStyle}
            selectedStyle={styles.selectedStyle}
          />

          {/* Height Range Selector */}
          <Text style={styles.filterLabel}>
            Height Range: {height[0]} - {height[1]} cm
          </Text>
          <MultiSlider
            values={height}
            min={100}
            max={300}
            step={1}
            onValuesChange={setHeightRange}
            sliderLength={width - 65}
            snapped
            markerStyle={styles.markerStyle}
            trackStyle={styles.trackStyle}
            selectedStyle={styles.selectedStyle}
          />

          {/* Dropdowns for all filters - with specific z-index to handle overlapping */}
          {renderDropdown('district', 'District')}
          {renderDropdown('maritalStatus', 'Marital Status')}
          {renderDropdown('religion', 'Religion')}
          {renderDropdown('caste', 'Caste')}
          {/* Only show Star field if Religion includes Hindu */}
          {filterValues.religion && filterValues.religion.includes('Hindu') &&
            renderDropdown('star', 'Star')}
          {renderDropdown('education', 'Education')}
          {renderDropdown('employedIn', 'Employed In')}


          <View style={styles.buttonContainer}>
            <TouchableOpacity style={styles.clearButton} onPress={clearFilters}>
              <Text style={styles.clearButtonText}>Clear Filters</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.searchButton} onPress={handleSearch}>
              <Text style={styles.buttonText}>Search</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.saveButton} onPress={saveSearch}>
              <Text style={styles.saveButtonText}>Save Search</Text>
            </TouchableOpacity>
          </View>
        </View>
      )}

      {loading && searchResults.length === 0 ? (
        <ActivityIndicator size="large" color="#007bff" style={styles.loader} />
      ) : (
        <FlatList
          style={[{ position: 'relative', zIndex: -1 }]}
          data={searchResults}
          keyExtractor={item => item.userId || item.id}
          renderItem={({ item, index }) => (
            <UserCard
              item={item}
              index={index}
              users={searchResults}
              screenName="ProfileDetailScreen"
            />
          )}
          ListEmptyComponent={
            <View style={styles.noResultsContainer}>
              <Text style={styles.noResultsText}>No matches found</Text>
            </View>
          }
          ListFooterComponent={
            isLoadingMore ? (
              <View style={styles.loadingMoreContainer}>
                <ActivityIndicator size="small" color="#007bff" />
                <Text style={styles.loadingMoreText}>Loading more...</Text>
              </View>
            ) : allUsersLoaded && searchResults.length > 0 ? (
              <View style={styles.endOfListContainer}>
                <Text style={styles.endOfListText}>You've seen all available matches! Try another search.</Text>
              </View>
            ) : null
          }
          onEndReached={() => {
            if (!isLoadingMore && !allUsersLoaded && lastDoc) {
              loadMoreUsers();
            }
          }}
          onEndReachedThreshold={0.3}
          contentContainerStyle={styles.listContainer}
          showsVerticalScrollIndicator={false}
          removeClippedSubviews={true}
          maxToRenderPerBatch={10}
          windowSize={21}
          initialNumToRender={PAGE_SIZE}
        />
      )}
    </KeyboardAvoidingView>
  );

  return (
    <FlatList
      showsVerticalScrollIndicator={false}
      data={[1]}
      renderItem={renderItem}
      keyExtractor={(item) => item.toString()}
      style={styles.container}
    />
  );
};

export default MatchesScreen;
