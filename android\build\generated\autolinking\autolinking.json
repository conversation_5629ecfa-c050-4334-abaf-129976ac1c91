{"root": "C:\\Dinu\\React-native\\MyApp\\matrimonialapp", "reactNativePath": "C:\\Dinu\\React-native\\MyApp\\matrimonialapp\\node_modules\\react-native", "reactNativeVersion": "0.75", "dependencies": {"@notifee/react-native": {"root": "C:\\Dinu\\React-native\\MyApp\\matrimonialapp\\node_modules\\@notifee\\react-native", "name": "@notifee/react-native", "platforms": {"ios": {"podspecPath": "C:\\Dinu\\React-native\\MyApp\\matrimonialapp\\node_modules\\@notifee\\react-native\\RNNotifee.podspec", "version": "9.1.8", "configurations": [], "scriptPhases": []}, "android": {"sourceDir": "C:\\Dinu\\React-native\\MyApp\\matrimonialapp\\node_modules\\@notifee\\react-native\\android", "packageImportPath": "import io.invertase.notifee.NotifeePackage;", "packageInstance": "new NotifeePackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "C:/Dinu/React-native/MyApp/matrimonialapp/node_modules/@notifee/react-native/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "@react-native-firebase/functions": {"root": "C:\\Dinu\\React-native\\MyApp\\matrimonialapp\\node_modules\\@react-native-firebase\\functions", "name": "@react-native-firebase/functions", "platforms": {"ios": {"podspecPath": "C:\\Dinu\\React-native\\MyApp\\matrimonialapp\\node_modules\\@react-native-firebase\\functions\\RNFBFunctions.podspec", "version": "21.3.0", "configurations": [], "scriptPhases": []}, "android": {"sourceDir": "C:\\Dinu\\React-native\\MyApp\\matrimonialapp\\node_modules\\@react-native-firebase\\functions\\android", "packageImportPath": "import io.invertase.firebase.functions.ReactNativeFirebaseFunctionsPackage;", "packageInstance": "new ReactNativeFirebaseFunctionsPackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "C:/Dinu/React-native/MyApp/matrimonialapp/node_modules/@react-native-firebase/functions/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "@react-native-firebase/messaging": {"root": "C:\\Dinu\\React-native\\MyApp\\matrimonialapp\\node_modules\\@react-native-firebase\\messaging", "name": "@react-native-firebase/messaging", "platforms": {"ios": {"podspecPath": "C:\\Dinu\\React-native\\MyApp\\matrimonialapp\\node_modules\\@react-native-firebase\\messaging\\RNFBMessaging.podspec", "version": "21.3.0", "configurations": [], "scriptPhases": []}, "android": {"sourceDir": "C:\\Dinu\\React-native\\MyApp\\matrimonialapp\\node_modules\\@react-native-firebase\\messaging\\android", "packageImportPath": "import io.invertase.firebase.messaging.ReactNativeFirebaseMessagingPackage;", "packageInstance": "new ReactNativeFirebaseMessagingPackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "C:/Dinu/React-native/MyApp/matrimonialapp/node_modules/@react-native-firebase/messaging/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "@react-native-picker/picker": {"root": "C:\\Dinu\\React-native\\MyApp\\matrimonialapp\\node_modules\\@react-native-picker\\picker", "name": "@react-native-picker/picker", "platforms": {"ios": {"podspecPath": "C:\\Dinu\\React-native\\MyApp\\matrimonialapp\\node_modules\\@react-native-picker\\picker\\RNCPicker.podspec", "version": "2.9.0", "configurations": [], "scriptPhases": []}, "android": {"sourceDir": "C:\\Dinu\\React-native\\MyApp\\matrimonialapp\\node_modules\\@react-native-picker\\picker\\android", "packageImportPath": "import com.reactnativecommunity.picker.RNCPickerPackage;", "packageInstance": "new RNCPickerPackage()", "buildTypes": [], "libraryName": "rnpicker", "componentDescriptors": ["RNCAndroidDialogPickerComponentDescriptor", "RNCAndroidDropdownPickerComponentDescriptor"], "cmakeListsPath": "C:/Dinu/React-native/MyApp/matrimonialapp/node_modules/@react-native-picker/picker/android/src/main/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "react-native-fs": {"root": "C:\\Dinu\\React-native\\MyApp\\matrimonialapp\\node_modules\\react-native-fs", "name": "react-native-fs", "platforms": {"ios": {"podspecPath": "C:\\Dinu\\React-native\\MyApp\\matrimonialapp\\node_modules\\react-native-fs\\RNFS.podspec", "version": "2.20.0", "configurations": [], "scriptPhases": []}, "android": {"sourceDir": "C:\\Dinu\\React-native\\MyApp\\matrimonialapp\\node_modules\\react-native-fs\\android", "packageImportPath": "import com.rnfs.RNFSPackage;", "packageInstance": "new RNFSPackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "C:/Dinu/React-native/MyApp/matrimonialapp/node_modules/react-native-fs/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "react-native-linear-gradient": {"root": "C:\\Dinu\\React-native\\MyApp\\matrimonialapp\\node_modules\\react-native-linear-gradient", "name": "react-native-linear-gradient", "platforms": {"ios": {"podspecPath": "C:\\Dinu\\React-native\\MyApp\\matrimonialapp\\node_modules\\react-native-linear-gradient\\BVLinearGradient.podspec", "version": "2.8.3", "configurations": [], "scriptPhases": []}, "android": {"sourceDir": "C:\\Dinu\\React-native\\MyApp\\matrimonialapp\\node_modules\\react-native-linear-gradient\\android", "packageImportPath": "import com.BV.LinearGradient.LinearGradientPackage;", "packageInstance": "new LinearGradientPackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "C:/Dinu/React-native/MyApp/matrimonialapp/node_modules/react-native-linear-gradient/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "react-native-razorpay": {"root": "C:\\Dinu\\React-native\\MyApp\\matrimonialapp\\node_modules\\react-native-razorpay", "name": "react-native-razorpay", "platforms": {"ios": {"podspecPath": "C:\\Dinu\\React-native\\MyApp\\matrimonialapp\\node_modules\\react-native-razorpay\\react-native-razorpay.podspec", "version": "2.3.0", "configurations": [], "scriptPhases": []}, "android": {"sourceDir": "C:\\Dinu\\React-native\\MyApp\\matrimonialapp\\node_modules\\react-native-razorpay\\android", "packageImportPath": "import com.razorpay.rn.RazorpayPackage;", "packageInstance": "new RazorpayPackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "C:/Dinu/React-native/MyApp/matrimonialapp/node_modules/react-native-razorpay/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "react-native-vector-icons": {"root": "C:\\Dinu\\React-native\\MyApp\\matrimonialapp\\node_modules\\react-native-vector-icons", "name": "react-native-vector-icons", "platforms": {"ios": {"podspecPath": "C:\\Dinu\\React-native\\MyApp\\matrimonialapp\\node_modules\\react-native-vector-icons\\RNVectorIcons.podspec", "version": "10.2.0", "configurations": [], "scriptPhases": []}, "android": {"sourceDir": "C:\\Dinu\\React-native\\MyApp\\matrimonialapp\\node_modules\\react-native-vector-icons\\android", "packageImportPath": "import com.oblador.vectoricons.VectorIconsPackage;", "packageInstance": "new VectorIconsPackage()", "buildTypes": [], "libraryName": "RNVectorIconsSpec", "componentDescriptors": [], "cmakeListsPath": "C:/Dinu/React-native/MyApp/matrimonialapp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "@react-native-community/datetimepicker": {"root": "C:\\Dinu\\React-native\\MyApp\\matrimonialapp\\node_modules\\@react-native-community\\datetimepicker", "name": "@react-native-community/datetimepicker", "platforms": {"ios": {"podspecPath": "C:\\Dinu\\React-native\\MyApp\\matrimonialapp\\node_modules\\@react-native-community\\datetimepicker\\RNDateTimePicker.podspec", "version": "8.2.0", "configurations": [], "scriptPhases": []}, "android": {"sourceDir": "C:\\Dinu\\React-native\\MyApp\\matrimonialapp\\node_modules\\@react-native-community\\datetimepicker\\android", "packageImportPath": "import com.reactcommunity.rndatetimepicker.RNDateTimePickerPackage;", "packageInstance": "new RNDateTimePickerPackage()", "buildTypes": [], "libraryName": "RNDateTimePickerCGen", "componentDescriptors": [], "cmakeListsPath": "C:/Dinu/React-native/MyApp/matrimonialapp/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "@react-native-firebase/app": {"root": "C:\\Dinu\\React-native\\MyApp\\matrimonialapp\\node_modules\\@react-native-firebase\\app", "name": "@react-native-firebase/app", "platforms": {"ios": {"podspecPath": "C:\\Dinu\\React-native\\MyApp\\matrimonialapp\\node_modules\\@react-native-firebase\\app\\RNFBApp.podspec", "version": "21.3.0", "configurations": [], "scriptPhases": [{"name": "[RNFB] Core Configuration", "path": "./ios_config.sh", "execution_position": "after_compile", "input_files": ["$(BUILT_PRODUCTS_DIR)/$(INFOPLIST_PATH)"]}]}, "android": {"sourceDir": "C:\\Dinu\\React-native\\MyApp\\matrimonialapp\\node_modules\\@react-native-firebase\\app\\android", "packageImportPath": "import io.invertase.firebase.app.ReactNativeFirebaseAppPackage;", "packageInstance": "new ReactNativeFirebaseAppPackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "C:/Dinu/React-native/MyApp/matrimonialapp/node_modules/@react-native-firebase/app/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "@react-native-firebase/auth": {"root": "C:\\Dinu\\React-native\\MyApp\\matrimonialapp\\node_modules\\@react-native-firebase\\auth", "name": "@react-native-firebase/auth", "platforms": {"ios": {"podspecPath": "C:\\Dinu\\React-native\\MyApp\\matrimonialapp\\node_modules\\@react-native-firebase\\auth\\RNFBAuth.podspec", "version": "21.3.0", "configurations": [], "scriptPhases": []}, "android": {"sourceDir": "C:\\Dinu\\React-native\\MyApp\\matrimonialapp\\node_modules\\@react-native-firebase\\auth\\android", "packageImportPath": "import io.invertase.firebase.auth.ReactNativeFirebaseAuthPackage;", "packageInstance": "new ReactNativeFirebaseAuthPackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "C:/Dinu/React-native/MyApp/matrimonialapp/node_modules/@react-native-firebase/auth/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "@react-native-firebase/firestore": {"root": "C:\\Dinu\\React-native\\MyApp\\matrimonialapp\\node_modules\\@react-native-firebase\\firestore", "name": "@react-native-firebase/firestore", "platforms": {"ios": {"podspecPath": "C:\\Dinu\\React-native\\MyApp\\matrimonialapp\\node_modules\\@react-native-firebase\\firestore\\RNFBFirestore.podspec", "version": "21.3.0", "configurations": [], "scriptPhases": []}, "android": {"sourceDir": "C:\\Dinu\\React-native\\MyApp\\matrimonialapp\\node_modules\\@react-native-firebase\\firestore\\android", "packageImportPath": "import io.invertase.firebase.firestore.ReactNativeFirebaseFirestorePackage;", "packageInstance": "new ReactNativeFirebaseFirestorePackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "C:/Dinu/React-native/MyApp/matrimonialapp/node_modules/@react-native-firebase/firestore/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "@react-native-firebase/storage": {"root": "C:\\Dinu\\React-native\\MyApp\\matrimonialapp\\node_modules\\@react-native-firebase\\storage", "name": "@react-native-firebase/storage", "platforms": {"ios": {"podspecPath": "C:\\Dinu\\React-native\\MyApp\\matrimonialapp\\node_modules\\@react-native-firebase\\storage\\RNFBStorage.podspec", "version": "21.3.0", "configurations": [], "scriptPhases": []}, "android": {"sourceDir": "C:\\Dinu\\React-native\\MyApp\\matrimonialapp\\node_modules\\@react-native-firebase\\storage\\android", "packageImportPath": "import io.invertase.firebase.storage.ReactNativeFirebaseStoragePackage;", "packageInstance": "new ReactNativeFirebaseStoragePackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "C:/Dinu/React-native/MyApp/matrimonialapp/node_modules/@react-native-firebase/storage/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "@react-native-masked-view/masked-view": {"root": "C:\\Dinu\\React-native\\MyApp\\matrimonialapp\\node_modules\\@react-native-masked-view\\masked-view", "name": "@react-native-masked-view/masked-view", "platforms": {"ios": {"podspecPath": "C:\\Dinu\\React-native\\MyApp\\matrimonialapp\\node_modules\\@react-native-masked-view\\masked-view\\RNCMaskedView.podspec", "version": "0.3.0", "configurations": [], "scriptPhases": []}, "android": {"sourceDir": "C:\\Dinu\\React-native\\MyApp\\matrimonialapp\\node_modules\\@react-native-masked-view\\masked-view\\android", "packageImportPath": "import org.reactnative.maskedview.RNCMaskedViewPackage;", "packageInstance": "new RNCMaskedViewPackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "C:/Dinu/React-native/MyApp/matrimonialapp/node_modules/@react-native-masked-view/masked-view/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "react-native-gesture-handler": {"root": "C:\\Dinu\\React-native\\MyApp\\matrimonialapp\\node_modules\\react-native-gesture-handler", "name": "react-native-gesture-handler", "platforms": {"ios": {"podspecPath": "C:\\Dinu\\React-native\\MyApp\\matrimonialapp\\node_modules\\react-native-gesture-handler\\RNGestureHandler.podspec", "version": "2.14.1", "configurations": [], "scriptPhases": []}, "android": {"sourceDir": "C:\\Dinu\\React-native\\MyApp\\matrimonialapp\\node_modules\\react-native-gesture-handler\\android", "packageImportPath": "import com.swmansion.gesturehandler.RNGestureHandlerPackage;", "packageInstance": "new RNGestureHandlerPackage()", "buildTypes": [], "libraryName": "rngesturehandler_codegen", "componentDescriptors": ["RNGestureHandlerButtonComponentDescriptor", "RNGestureHandlerRootViewComponentDescriptor"], "cmakeListsPath": "C:/Dinu/React-native/MyApp/matrimonialapp/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "react-native-image-picker": {"root": "C:\\Dinu\\React-native\\MyApp\\matrimonialapp\\node_modules\\react-native-image-picker", "name": "react-native-image-picker", "platforms": {"ios": {"podspecPath": "C:\\Dinu\\React-native\\MyApp\\matrimonialapp\\node_modules\\react-native-image-picker\\react-native-image-picker.podspec", "version": "7.2.3", "configurations": [], "scriptPhases": []}, "android": {"sourceDir": "C:\\Dinu\\React-native\\MyApp\\matrimonialapp\\node_modules\\react-native-image-picker\\android", "packageImportPath": "import com.imagepicker.ImagePickerPackage;", "packageInstance": "new ImagePickerPackage()", "buildTypes": [], "libraryName": "RNImagePickerSpec", "componentDescriptors": [], "cmakeListsPath": "C:/Dinu/React-native/MyApp/matrimonialapp/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "react-native-reanimated": {"root": "C:\\Dinu\\React-native\\MyApp\\matrimonialapp\\node_modules\\react-native-reanimated", "name": "react-native-reanimated", "platforms": {"ios": {"podspecPath": "C:\\Dinu\\React-native\\MyApp\\matrimonialapp\\node_modules\\react-native-reanimated\\RNReanimated.podspec", "version": "3.17.1", "configurations": [], "scriptPhases": []}, "android": {"sourceDir": "C:\\Dinu\\React-native\\MyApp\\matrimonialapp\\node_modules\\react-native-reanimated\\android", "packageImportPath": "import com.swmansion.reanimated.ReanimatedPackage;", "packageInstance": "new ReanimatedPackage()", "buildTypes": [], "libraryName": "rnreanimated", "componentDescriptors": [], "cmakeListsPath": "C:/Dinu/React-native/MyApp/matrimonialapp/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "react-native-safe-area-context": {"root": "C:\\Dinu\\React-native\\MyApp\\matrimonialapp\\node_modules\\react-native-safe-area-context", "name": "react-native-safe-area-context", "platforms": {"ios": {"podspecPath": "C:\\Dinu\\React-native\\MyApp\\matrimonialapp\\node_modules\\react-native-safe-area-context\\react-native-safe-area-context.podspec", "version": "4.11.1", "configurations": [], "scriptPhases": []}, "android": {"sourceDir": "C:\\Dinu\\React-native\\MyApp\\matrimonialapp\\node_modules\\react-native-safe-area-context\\android", "packageImportPath": "import com.th3rdwave.safeareacontext.SafeAreaContextPackage;", "packageInstance": "new SafeAreaContextPackage()", "buildTypes": [], "libraryName": "safeareacontext", "componentDescriptors": ["RNCSafeAreaProviderComponentDescriptor", "RNCSafeAreaViewComponentDescriptor"], "cmakeListsPath": "C:/Dinu/React-native/MyApp/matrimonialapp/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "react-native-screens": {"root": "C:\\Dinu\\React-native\\MyApp\\matrimonialapp\\node_modules\\react-native-screens", "name": "react-native-screens", "platforms": {"ios": {"podspecPath": "C:\\Dinu\\React-native\\MyApp\\matrimonialapp\\node_modules\\react-native-screens\\RNScreens.podspec", "version": "3.34.1", "configurations": [], "scriptPhases": []}, "android": {"sourceDir": "C:\\Dinu\\React-native\\MyApp\\matrimonialapp\\node_modules\\react-native-screens\\android", "packageImportPath": "import com.swmansion.rnscreens.RNScreensPackage;", "packageInstance": "new RNScreensPackage()", "buildTypes": [], "libraryName": "rnscreens", "componentDescriptors": ["RNSFullWindowOverlayComponentDescriptor", "RNSScreenContainerComponentDescriptor", "RNSScreenNavigationContainerComponentDescriptor", "RNSScreenStackHeaderConfigComponentDescriptor", "RNSScreenStackHeaderSubviewComponentDescriptor", "RNSScreenStackComponentDescriptor", "RNSSearchBarComponentDescriptor", "RNSScreenComponentDescriptor", "RNSModalScreenComponentDescriptor"], "cmakeListsPath": "C:/Dinu/React-native/MyApp/matrimonialapp/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}}, "commands": [{"name": "bundle", "description": "Build the bundle for the provided JavaScript entry file.", "options": [{"name": "--entry-file <path>", "description": "Path to the root JS file, either absolute or relative to JS root"}, {"name": "--platform <string>", "description": "Either \"ios\" or \"android\"", "default": "ios"}, {"name": "--transformer <string>", "description": "Specify a custom transformer to be used"}, {"name": "--dev [boolean]", "description": "If false, warnings are disabled and the bundle is minified", "default": true}, {"name": "--minify [boolean]", "description": "Allows overriding whether bundle is minified. This defaults to false if dev is true, and true if dev is false. Disabling minification can be useful for speeding up production builds for testing purposes."}, {"name": "--bundle-output <string>", "description": "File name where to store the resulting bundle, ex. /tmp/groups.bundle"}, {"name": "--bundle-encoding <string>", "description": "Encoding the bundle should be written in (https://nodejs.org/api/buffer.html#buffer_buffer).", "default": "utf8"}, {"name": "--max-workers <number>", "description": "Specifies the maximum number of workers the worker-pool will spawn for transforming files. This defaults to the number of the cores available on your machine."}, {"name": "--sourcemap-output <string>", "description": "File name where to store the sourcemap file for resulting bundle, ex. /tmp/groups.map"}, {"name": "--sourcemap-sources-root <string>", "description": "Path to make sourcemap's sources entries relative to, ex. /root/dir"}, {"name": "--sourcemap-use-absolute-path", "description": "Report SourceMapURL using its full path", "default": false}, {"name": "--assets-dest <string>", "description": "Directory name where to store assets referenced in the bundle"}, {"name": "--unstable-transform-profile <string>", "description": "Experimental, transform JS for a specific JS engine. Currently supported: hermes, hermes-canary, default", "default": "default"}, {"name": "--asset-catalog-dest [string]", "description": "Path where to create an iOS Asset Catalog for images"}, {"name": "--reset-cache", "description": "Removes cached files", "default": false}, {"name": "--read-global-cache", "description": "Try to fetch transformed JS code from the global cache, if configured.", "default": false}, {"name": "--config <string>", "description": "Path to the CLI configuration file"}, {"name": "--resolver-option <string...>", "description": "Custom resolver options of the form key=value. URL-encoded. May be specified multiple times."}]}, {"name": "start", "description": "Start the React Native development server.", "options": [{"name": "--port <number>"}, {"name": "--host <string>", "default": ""}, {"name": "--projectRoot <path>", "description": "Path to a custom project root"}, {"name": "--watchFolders <list>", "description": "Specify any additional folders to be added to the watch list"}, {"name": "--assetPlugins <list>", "description": "Specify any additional asset plugins to be used by the packager by full filepath"}, {"name": "--sourceExts <list>", "description": "Specify any additional source extensions to be used by the packager"}, {"name": "--max-workers <number>", "description": "Specifies the maximum number of workers the worker-pool will spawn for transforming files. This defaults to the number of the cores available on your machine."}, {"name": "--transformer <string>", "description": "Specify a custom transformer to be used"}, {"name": "--reset-cache, --resetCache", "description": "Removes cached files"}, {"name": "--custom-log-reporter-path, --customLogReporterPath <string>", "description": "Path to a JavaScript file that exports a log reporter as a replacement for TerminalReporter"}, {"name": "--https", "description": "Enables https connections to the server"}, {"name": "--key <path>", "description": "Path to custom SSL key"}, {"name": "--cert <path>", "description": "Path to custom SSL cert"}, {"name": "--config <string>", "description": "Path to the CLI configuration file"}, {"name": "--no-interactive", "description": "Disables interactive mode"}, {"name": "--experimental-debugger", "description": "[Experimental] Enable the new debugger experience and 'j' to debug. This enables the new frontend experience only: connection reliability and some basic features are unstable in this release."}]}, {"name": "codegen", "options": [{"name": "--path <path>", "description": "Path to the React Native project root.", "default": "C:\\Dinu\\React-native\\MyApp\\matrimonialapp"}, {"name": "--platform <string>", "description": "Target platform. Supported values: \"android\", \"ios\", \"all\".", "default": "all"}, {"name": "--outputPath <path>", "description": "Path where generated artifacts will be output to."}]}, {"name": "log-ios", "description": "starts iOS device syslog tail", "options": [{"name": "-i --interactive", "description": "Explicitly select simulator to tail logs from. By default it will tail logs from the first booted and available simulator."}]}, {"name": "run-ios", "description": "builds your app and starts it on iOS simulator", "examples": [{"desc": "Run on a different simulator, e.g. iPhone SE (2nd generation)", "cmd": "npx react-native run-ios --simulator \"iPhone SE (2nd generation)\""}, {"desc": "Run on a connected device, e.g. <PERSON>'s iPhone", "cmd": "npx react-native run-ios --device \"Max's iPhone\""}, {"desc": "Run on the AppleTV simulator", "cmd": "npx react-native run-ios --simulator \"Apple TV\"  --scheme \"helloworld-tvOS\""}], "options": [{"name": "--no-packager", "description": "Do not launch packager while running the app"}, {"name": "--port <number>", "default": 8081}, {"name": "--terminal <string>", "description": "Launches the Metro Bundler in a new window using the specified terminal path.", "default": "cmd.exe"}, {"name": "--binary-path <string>", "description": "Path relative to project root where pre-built .app binary lives."}, {"name": "--list-devices", "description": "List all available iOS devices and simulators and let you choose one to run the app. "}, {"name": "--udid <string>", "description": "Explicitly set the device to use by UDID"}, {"name": "--simulator <string>", "description": "Explicitly set the simulator to use. Optionally set the iOS version between parentheses at the end to match an exact version: \"iPhone 15 (17.0)\""}, {"name": "--mode <string>", "description": "Explicitly set the scheme configuration to use. This option is case sensitive."}, {"name": "--scheme <string>", "description": "Explicitly set Xcode scheme to use"}, {"name": "--destination <string>", "description": "Explicitly extend destination e.g. \"arch=x86_64\""}, {"name": "--verbose", "description": "Do not use xcbeautify or xcpretty even if installed"}, {"name": "--xcconfig [string]", "description": "Explicitly set xcconfig to use"}, {"name": "--buildFolder <string>", "description": "Location for iOS build artifacts. Corresponds to Xcode's \"-derivedDataPath\"."}, {"name": "--extra-params <string>", "description": "Custom params that will be passed to xcodebuild command."}, {"name": "--target <string>", "description": "Explicitly set Xcode target to use."}, {"name": "-i --interactive", "description": "Explicitly select which scheme and configuration to use before running a build"}, {"name": "--force-pods", "description": "Force CocoaPods installation"}, {"name": "--device [string]", "description": "Explicitly set the device to use by name or by unique device identifier . If the value is not provided,the app will run on the first available physical device."}]}, {"name": "build-ios", "description": "builds your app for iOS platform", "examples": [{"desc": "Build the app for all iOS devices in Release mode", "cmd": "npx react-native build-ios --mode \"Release\""}], "options": [{"name": "--mode <string>", "description": "Explicitly set the scheme configuration to use. This option is case sensitive."}, {"name": "--scheme <string>", "description": "Explicitly set Xcode scheme to use"}, {"name": "--destination <string>", "description": "Explicitly extend destination e.g. \"arch=x86_64\""}, {"name": "--verbose", "description": "Do not use xcbeautify or xcpretty even if installed"}, {"name": "--xcconfig [string]", "description": "Explicitly set xcconfig to use"}, {"name": "--buildFolder <string>", "description": "Location for iOS build artifacts. Corresponds to Xcode's \"-derivedDataPath\"."}, {"name": "--extra-params <string>", "description": "Custom params that will be passed to xcodebuild command."}, {"name": "--target <string>", "description": "Explicitly set Xcode target to use."}, {"name": "-i --interactive", "description": "Explicitly select which scheme and configuration to use before running a build"}, {"name": "--force-pods", "description": "Force CocoaPods installation"}, {"name": "--device [string]", "description": "Explicitly set the device to use by name or by unique device identifier . If the value is not provided,the app will run on the first available physical device."}]}, {"name": "log-android", "description": "starts logki<PERSON>"}, {"name": "run-android", "description": "builds your app and starts it on a connected Android emulator or device", "options": [{"name": "--mode <string>", "description": "Specify your app's build variant"}, {"name": "--tasks <list>", "description": "Run custom Gradle tasks. By default it's \"assembleDebug\". Will override passed mode and variant arguments."}, {"name": "--active-arch-only", "description": "Build native libraries only for the current device architecture for debug builds.", "default": false}, {"name": "--extra-params <string>", "description": "Custom params passed to gradle build command"}, {"name": "-i --interactive", "description": "Explicitly select build type and flavour to use before running a build"}, {"name": "--no-packager", "description": "Do not launch packager while running the app"}, {"name": "--port <number>", "default": 8081}, {"name": "--terminal <string>", "description": "Launches the Metro Bundler in a new window using the specified terminal path.", "default": "cmd.exe"}, {"name": "--appId <string>", "description": "Specify an applicationId to launch after build. If not specified, `package` from AndroidManifest.xml will be used.", "default": ""}, {"name": "--appIdSuffix <string>", "description": "Specify an applicationIdSuffix to launch after build.", "default": ""}, {"name": "--main-activity <string>", "description": "Name of the activity to start"}, {"name": "--device <string>", "description": "Explicitly set the device to use by name. The value is not required if you have a single device connected."}, {"name": "--deviceId <string>", "description": "**DEPRECATED** Builds your app and starts it on a specific device/simulator with the given device id (listed by running \"adb devices\" on the command line)."}, {"name": "--list-devices", "description": "Lists all available Android devices and simulators and let you choose one to run the app", "default": false}, {"name": "--binary-path <string>", "description": "Path relative to project root where pre-built .apk binary lives."}, {"name": "--user <number>", "description": "Id of the User Profile you want to install the app on."}]}, {"name": "build-android", "description": "builds your app", "options": [{"name": "--mode <string>", "description": "Specify your app's build variant"}, {"name": "--tasks <list>", "description": "Run custom Gradle tasks. By default it's \"assembleDebug\". Will override passed mode and variant arguments."}, {"name": "--active-arch-only", "description": "Build native libraries only for the current device architecture for debug builds.", "default": false}, {"name": "--extra-params <string>", "description": "Custom params passed to gradle build command"}, {"name": "-i --interactive", "description": "Explicitly select build type and flavour to use before running a build"}]}], "healthChecks": [], "platforms": {"ios": {}, "android": {}}, "assets": ["./node_modules/react-native-vector-icons/Fonts"], "project": {"ios": {"sourceDir": "C:\\Dinu\\React-native\\MyApp\\matrimonialapp\\ios", "xcodeProject": {"name": "MTRMNLApp.xcodeproj", "path": ".", "isWorkspace": false}, "assets": []}, "android": {"sourceDir": "C:\\Dinu\\React-native\\MyApp\\matrimonialapp\\android", "appName": "app", "packageName": "malayali.match.com", "applicationId": "malayali.match.com", "mainActivity": ".MainActivity", "assets": []}}}