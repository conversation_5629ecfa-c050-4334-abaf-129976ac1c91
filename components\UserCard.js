import React from 'react';
import { View, Text, Image, TouchableOpacity, Dimensions } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import styles from '../styles/UserCardStyles';

const UserCard = ({
  item,
  users,
  index,
  screenName,
  onPressRemove,
  removeFavourite
}) => {
  const navigation = useNavigation();
  const screenWidth = Dimensions.get('window').width;

  const profileImageURI = item?.profileImage1
    ? { uri: item.profileImage1 }
    : require('../assets/splash.png'); // Default image

  // Helper function to check if a field has a value
  const hasValue = (field) => {
    return field && field.toString().trim() !== '';
  };

  // Helper function to get the display value for fields that might have custom values
  const getDisplayValue = (standardField, customField) => {
    // If the standard field is "Other" and a custom field exists, use the custom field
    if (standardField === 'Other' && hasValue(customField)) {
      return customField;
    }
    // Otherwise use the standard field
    return standardField;
  };

  return (
    <View style={[styles.card, { width: screenWidth * 0.9 }]}>
      <TouchableOpacity
        style={styles.cardContent}
        onPress={() =>
          navigation.navigate(screenName, { users, index })
        }
      >
        <Image source={profileImageURI} style={styles.profileImage} />
        <View style={styles.detailsContainer}>
          <Text style={styles.name}>{item?.fullName ? item.fullName.toUpperCase() : 'N/A'}</Text>

          <View style={styles.infoRow}>
            <Text style={styles.ageLocation}>
              {item?.age ? `${item.age} years` : 'Age not available'}
            </Text>
          </View>

          {/* Caste field */}
          {(hasValue(item?.caste) || hasValue(item?.customCaste)) && (
            <Text style={styles.info}>
              <Text style={styles.infoLabel}>Caste:</Text> {
                getDisplayValue(item.caste, item.customCaste)
              }
            </Text>
          )}

          {/* Only show fields if they have values */}
          {hasValue(item?.star) && (
            <Text style={styles.info}>
              <Text style={styles.infoLabel}>Star:</Text> {item.star}
            </Text>
          )}

          {(hasValue(item?.highestEducation) || hasValue(item?.customEducation)) && (
            <Text style={styles.info} numberOfLines={1} ellipsizeMode="tail">
              <Text style={styles.infoLabel}>Education:</Text> {
                getDisplayValue(item.customEducation || item.highestEducation)
              }
            </Text>
          )}

          {(hasValue(item?.state) || hasValue(item?.city)) && (
            <Text style={styles.info}>
              <Text style={styles.infoLabel}>Location:</Text> {[
                hasValue(item?.state) ? item.state : null,
                hasValue(item?.city) ? item.city : null
              ].filter(Boolean).join(', ')}
            </Text>
          )}
        </View>
      </TouchableOpacity>

      {removeFavourite && (
        <TouchableOpacity
          style={styles.removeIcon}
          onPress={() => onPressRemove(item.userUid)}
        >
          <Text style={styles.favouriteText}>{"♥"}</Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

export default UserCard;
