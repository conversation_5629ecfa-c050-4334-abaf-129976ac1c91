import { Dimensions, StyleSheet } from 'react-native'
import { variables } from './Variables'

const windowHeight = Dimensions.get('window').height;

const { themeBGColor, white, gray1, red, inputBtnWidth, black, green } = variables;

export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: white,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
    paddingTop: 0,
    bottom: 0,
  },
  radioContainer: {
    width: '80%',
    marginBottom: 10,
  },
  wrapper: {
    flex: 1,
    backgroundColor: white,
  },
  label: {
    fontSize: 12,
  },
  input: {
    paddingVertical: 10,
    marginBottom: 20,
    borderBottomWidth: 1,
    borderBottomColor: gray1,
    width: inputBtnWidth,
  },
  button: {
    backgroundColor: themeBGColor,
    paddingVertical: 12,
    alignItems: 'center',
    width: inputBtnWidth
  },
  buttonText: {
    color: white,
    fontSize: 14,
  },
  apploginButtonText: {
    fontSize: 14,
    alignSelf: "center",
    color: white,
  },
  loaderContainer: {
    position: 'absolute',
    top: 0, left: 0, right: 0, bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.3)',
    justifyContent: 'center',
    alignItems: 'center',
    height: windowHeight,
    zIndex: 1000
  },
  appButtonContainer: {
    paddingVertical: 10,
    paddingHorizontal: 12
  },
  inputError: {
    paddingVertical: 10,
    paddingHorizontal: 10,
    marginBottom: 20,
    borderBottomWidth: 1,
    width: inputBtnWidth,
    borderBottomColor: red,
  },
  error: {
    color: red,
    marginTop: -15,
    marginBottom: 10,
    marginLeft: 5,
    width: inputBtnWidth,
    fontSize: 11,
  },
  alreadyAccountText: {
    color: green,
    fontSize: 13,
  },
  terms: {
    flexDirection: 'row',
    color: black,
    marginBottom: 15,
    marginTop: 7,
  },
  termsAgreeText: {
    fontSize: 10.5,
  },
  termsText: {
    textDecorationLine: 'underline',
    paddingLeft: 5,
    fontSize: 10.5,
  },
  helpSection: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 20,
  },
  helpText: {
    marginLeft: 10,
    fontSize: 16,
    color: '#000',
  },
  progressContainer: {
    marginTop: 20,
    marginBottom: 25,
    width: '100%',
    alignItems: 'center',
  },
  progressText: {
    textAlign: 'center',
    marginTop: 12,
    color: '#555',
    fontSize: 14,
    fontWeight: '500',
    letterSpacing: 0.5,
  },
  stepIndicatorContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  stepIndicatorWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  stepIndicator: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
  },
  stepIndicatorActive: {
    backgroundColor: themeBGColor,
    borderColor: themeBGColor,
    shadowColor: themeBGColor,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 3,
  },
  stepIndicatorInactive: {
    backgroundColor: '#F5F5F5',
    borderColor: '#E0E0E0',
  },
  stepIndicatorText: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  stepIndicatorTextActive: {
    color: white,
  },
  stepIndicatorTextInactive: {
    color: '#999',
  },
  stepConnector: {
    width: 40,
    height: 2,
    marginHorizontal: 8,
  },
  stepConnectorActive: {
    backgroundColor: themeBGColor,
  },
  stepConnectorInactive: {
    backgroundColor: '#E0E0E0',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 20,
  },
  nextButton: {
    backgroundColor: '#4CAF50',
    padding: 15,
    borderRadius: 5,
    alignItems: 'center',
    marginTop: 20,
    minWidth: 180,
  },
  backButton: {
    backgroundColor: '#9E9E9E',
    padding: 15,
    borderRadius: 5,
    alignItems: 'center',
    marginTop: 20,
    flex: 1,
    marginRight: 10,
  },
  submitButton: {
    backgroundColor: '#4CAF50',
    padding: 15,
    borderRadius: 5,
    alignItems: 'center',
    marginTop: 20,
    flex: 1,
    marginLeft: 10,
  },
  charCount: {
    textAlign: 'right',
    color: '#666',
    fontSize: 12,
    marginTop: 5,
  },
  helperText: {
    fontSize: 12,
    color: '#666',
    marginTop: -15,
    marginBottom: 10,
    marginLeft: 5,
    width: inputBtnWidth,
  },
});