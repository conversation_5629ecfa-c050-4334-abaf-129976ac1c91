import React, { useState, useEffect, useLayoutEffect } from 'react';
import {
  View,
  Text,
  Image,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  ScrollView,
  Alert,
  Modal,
  TextInput,
  ActivityIndicator,
  Pressable,
  Platform,
} from 'react-native';
import Swiper from 'react-native-swiper';
import { useNavigation } from '@react-navigation/native';
import LinearGradient from 'react-native-linear-gradient';
import { variables } from '../styles/Variables';
import firestore from '@react-native-firebase/firestore';
import auth from '@react-native-firebase/auth';
import functions from '@react-native-firebase/functions';
import RazorpayCheckout from 'react-native-razorpay';
import { usePreferences } from '../contexts/PreferencesContext';
import HeaderBalance from '../components/HeaderBalance';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import Ionicons from 'react-native-vector-icons/Ionicons';

const screenWidth = Dimensions.get('window').width;

const ProfileDetailScreen = ({ route }) => {
  const { users, index, isFavouriteUser } = route.params;
  const navigation = useNavigation();
  const [isPhoneVisible, setIsPhoneVisible] = useState(false);
  const [isFavourite, setIsFavourite] = useState(isFavouriteUser);
  const [usersIndex, setUserIndex] = useState(index);
  const [isNotInterested, setIsNotInterested] = useState(false);

  // Modal and payment states
  const [isModalVisible, setModalVisible] = useState(false);
  const [activeTab, setActiveTab] = useState('plans');
  const [amount, setAmount] = useState(0);
  const [selectedPlanRange, setSelectedPlanRange] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");

  // New state: "view" vs "recharge" context.
  const [paymentContext, setPaymentContext] = useState("view");

  const userId = auth().currentUser.uid;
  const userDocRef = firestore().collection('users').doc(userId);
  const selectedUserId = users[usersIndex]?.userUid;
  const { preferences } = usePreferences();
  const enableNotification = preferences?.FCMnotification?.enableNotification;
  const fetchedPlans = preferences?.Payment?.plans;
  const minAmount = preferences?.Payment?.minAmount;

  useLayoutEffect(() => {
    navigation.setOptions({
      headerTitle: () => (
        <HeaderBalance
          showRecharge={true}
          onRecharge={() => {
            setPaymentContext("recharge");
            setModalVisible(true);
            setActiveTab('plans');
          }}
        />
      ),
    });
  }, [navigation]);

  useEffect(() => {
    checkIfFavourite();
    checkIfNotInterested();
  }, [selectedUserId]);

  useEffect(() => {
    async function updateViewedProfile() {
      try {
        if (selectedUserId) {
          await userDocRef.update({
            viewedProfileIds: firestore.FieldValue.arrayUnion(selectedUserId),
          });
        }
      } catch (error) {
        console.error('Error updating viewedProfileIds:', error);
      }
    }

    updateViewedProfile();
  }, [selectedUserId]);

  const checkIfFavourite = async (userIndexParam = usersIndex) => {
    try {
      const userDoc = await userDocRef.get();
      const favourites = userDoc.data()?.favourites || [];
      setUserIndex(userIndexParam);
      setIsFavourite(favourites.includes(users[userIndexParam]?.userUid));
    } catch (error) {
      console.error("Error checking favourite status:", error);
    }
  };

  const checkIfNotInterested = async (userIndexParam = usersIndex) => {
    try {
      const userDoc = await userDocRef.get();
      const notInterested = userDoc.data()?.notInterested || [];
      setUserIndex(userIndexParam);
      setIsNotInterested(notInterested.includes(users[userIndexParam]?.userUid));
    } catch (error) {
      console.error("Error checking not interested status:", error);
    }
  };

  const handleNotInterested = async () => {
    try {
      const targetUserDocRef = firestore().collection('users').doc(selectedUserId);
      const targetUserDoc = await targetUserDocRef.get();
      if (!targetUserDoc.exists) return;
      setIsNotInterested(true);
      setIsFavourite(false);
      const batch = firestore().batch();

      batch.update(userDocRef, {
        notInterested: firestore.FieldValue.arrayUnion(selectedUserId),
      });
      batch.update(userDocRef, {
        favourites: firestore.FieldValue.arrayRemove(selectedUserId),
      });
      batch.update(userDocRef, {
        likedByOthers: firestore.FieldValue.arrayRemove(userId),
      });

      await batch.commit();

    } catch (error) {
      setIsNotInterested(false);
      console.error("Error updating not interested status:", error);
    }
  };

  const sendFavouriteNotification = async (targetUserDoc) => {
    try {
      const fcmToken = targetUserDoc.data()?.fcmToken;
      const fullName = targetUserDoc.data()?.fullName;
      const userDoc = await userDocRef.get();
      const currentUserFirstName = userDoc.data()?.fullName || 'Someone';

      if (!fcmToken) return;

      const data = {
        tokens: [fcmToken],
        title: `Hello ${fullName}!`,
        body: `Awesome news! ${currentUserFirstName} just liked your profile! 💖 Take a look now!`,
        data: {
          screen: 'LikedByOthers',
          type: 'profile_like',
        },
      };

      const sendNotification = functions().httpsCallable('sendNotification');
      await sendNotification(data);
    } catch (error) {
      console.error('Error calling Cloud Function:', error);
    }
  };

  const handleToggleFavourite = async () => {
    try {
      const targetUserDocRef = firestore().collection('users').doc(selectedUserId);
      const targetUserDoc = await targetUserDocRef.get();
      if (!targetUserDoc.exists) return;
      const newFavouriteState = !isFavourite;
      setIsFavourite(newFavouriteState);

      const batch = firestore().batch();

      if (newFavouriteState) {
        setIsNotInterested(false);
        batch.update(userDocRef, {
          favourites: firestore.FieldValue.arrayUnion(selectedUserId),
        });
        batch.update(userDocRef, {
          notInterested: firestore.FieldValue.arrayRemove(selectedUserId),
        });
        batch.update(targetUserDocRef, {
          likedByOthers: firestore.FieldValue.arrayUnion(userId),
        });

        if (enableNotification) {
          sendFavouriteNotification(targetUserDoc);
        }
      } else {
        batch.update(userDocRef, {
          favourites: firestore.FieldValue.arrayRemove(selectedUserId),
        });
        batch.update(targetUserDocRef, {
          likedByOthers: firestore.FieldValue.arrayRemove(userId),
        });
      }

      await batch.commit();

    } catch (error) {
      setIsFavourite(!isFavourite);
      console.error("Error updating likedByOthers:", error);
    }
  };

  const updatePayment = async () => {

    if (paymentContext === "recharge") return;

    const userDoc = await userDocRef.get();
    const userNumberViewed = userDoc.data()?.numberViewedUserIds || [];
    const isAlreadyViewed = userNumberViewed.includes(users[usersIndex]?.userUid);

    if (isAlreadyViewed) {
      setIsPhoneVisible(true);
      return;
    }

    let paidAmount = userDoc.data()?.paidAmount || 0;
    if (paidAmount >= minAmount) {
      setModalVisible(false);
      setIsPhoneVisible(true);
      const updatedBalance = Number(paidAmount - minAmount);
      const viewedUserUid = users[usersIndex]?.userUid;
      let updatePayload = {
        paidAmount: updatedBalance,
      };
      if (viewedUserUid !== undefined) {
        updatePayload.numberViewedUserIds = firestore.FieldValue.arrayUnion(viewedUserUid);
      }
      await userDocRef.update(updatePayload);
    } else {
      setModalVisible(true);
      setActiveTab('plans');
    }
  };

  const handleViewPhoneNumberRequest = async () => {
    updatePayment();
  };

  // Helper to compute activePlan object based on updatedBalance.
  const getActivePlan = (updatedBalance) => {
    let planDays = 0;
    if (fetchedPlans) {
      for (const key in fetchedPlans) {
        if (fetchedPlans.hasOwnProperty(key)) {
          const [minRange, maxRange] = fetchedPlans[key]
            .split('-')
            .map(val => Number(val));
          if (updatedBalance >= minRange && updatedBalance <= maxRange) {
            planDays = Number(key);
            break;
          }
        }
      }
    }
    return planDays > 0
      ? {
        planDays: planDays,
        planAmount: updatedBalance,
        purchaseDate: firestore.FieldValue.serverTimestamp(),
      }
      : null;
  };

  // Payment submission: differentiate behavior by paymentContext.
  const handlePayment = async () => {
    const viewedUserUid = users[usersIndex]?.userUid;
    if (amount <= 0) {
      setErrorMessage("Please enter a valid amount");
    }
    if (!amount || !viewedUserUid) return;

    if (amount > 2500) {
      setErrorMessage("Amount cannot be greater than ₹2500.");
      return;
    }

    try {
      setIsLoading(true);
      setModalVisible(false);

      // functions().useEmulator('********', 5001);
      const createOrder = functions().httpsCallable('createRazorpayOrder');
      const userDoc = await userDocRef.get();
      const currrentUserId = userDoc.data()?.userId || '';
      const orderResponse = await createOrder({ amount: amount, userId: currrentUserId });
      const { id } = orderResponse.data;

      const options = {
        description: 'Malayali Match Payments',
        image: 'https://your-logo-url.com/logo.png',
        currency: 'INR',
        key: 'rzp_test_PZS9x4kZtZKBGS',
        amount: parseInt(amount) * 100,
        order_id: id,
        name: 'Malayali Match',
        prefill: {
          email: auth().currentUser.email,
          name: auth().currentUser.displayName || ''
        },
        theme: { color: variables.themeBGColor },
        method: {
          card: true,
          netbanking: true,
          wallet: false,
          upi: true,
          emi: false,
          paylater: false
        }
      };

      RazorpayCheckout.open(options)
        .then(async (data) => {
          // functions().useEmulator('********', 5001);
          setModalVisible(false);
          const verifySignature = functions().httpsCallable('verifyRazorpaySignature');
          const verifyResponse = await verifySignature({
            razorpay_payment_id: data.razorpay_payment_id,
            razorpay_order_id: data.razorpay_order_id,
            razorpay_signature: data.razorpay_signature,
          });
          if (!verifyResponse.data.valid) {
            Alert.alert("Payment service is not available now", "Please try after some time");
            setIsLoading(false);
            return;
          }
          // Retrieve the current balance.
          let paidAmount = userDoc.data()?.paidAmount || 0;
          let updatedBalance = 0;

          if (paymentContext === "view") {
            updatedBalance = Number(paidAmount) + Number(amount) - minAmount;
            let updatePayload = {
              paidAmount: updatedBalance,
              activePlan: updatedBalance < minAmount ? null : getActivePlan(updatedBalance)
            };
            if (viewedUserUid !== undefined) {
              updatePayload.numberViewedUserIds = firestore.FieldValue.arrayUnion(viewedUserUid);
            }
            await userDocRef.update(updatePayload);
            setIsPhoneVisible(true);
          } else {
            updatedBalance = Number(paidAmount) + Number(amount);
            let updatePayload = {
              paidAmount: updatedBalance,
              activePlan: updatedBalance < minAmount ? null : getActivePlan(updatedBalance)
            };
            await userDocRef.update(updatePayload);
            setPaymentContext("view");
          }
          setIsLoading(false);
        })
        .catch((error) => {
          Alert.alert("Payment service is not available now", "Please try after some time");
          setIsLoading(false);
        });
    } catch (error) {
      Alert.alert("Payment service is not available now, Please try payment after some time");
      setIsLoading(false);
    }
  };

  // Updated handleImagePress: filter out null image URLs.
  const handleImagePress = (item) => {
    const images = [
      item.profileImage1,
      item.profileImage2,
      item.profileImage3,
    ].filter(url => url);
    if (images.length === 0) {
      return;
    }
    navigation.navigate('ImageSwiperScreen', { images });
  };

  // Modified renderCarouselItem that accepts the slide's index.
  const renderCarouselItem = (item, slideIndex) => (
    <ScrollView contentContainerStyle={styles.scrollContainer} showsVerticalScrollIndicator={false}>
      <View style={styles.container}>
        <TouchableOpacity onPress={() => handleImagePress(item)}>
          <Image
            source={
              item.profileImage1
                ? { uri: item.profileImage1 }
                : require('../assets/splash.png')
            }
            style={styles.profileImage}
          />
        </TouchableOpacity>
        <View style={styles.overlayContainer}>
          <LinearGradient
            colors={['transparent', 'rgba(0,0,0,1)']} // Reversed colors
            start={{ x: 1, y: 0 }} // Start from bottom
            end={{ x: 1, y: 1 }} // End at top
            style={styles.gradient}
          >
            <Text style={styles.userName}>{`${item.fullName}`}</Text>
            <Text style={styles.userDetail}>{`User ID: ${item.userId}`}</Text>
          </LinearGradient>
        </View>
        <View style={styles.interestButtonsContainer}>
          <TouchableOpacity style={[styles.favButton, isFavourite && styles.interestButtonActive]}
            onPress={handleToggleFavourite}
          >
            <FontAwesome name="heart" size={15} color={isFavourite ? 'red' : '#fff'} />
            <Text style={[styles.interestButtonText, isFavourite && styles.interestButtonActive]}>Interested</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.interestButton, isNotInterested && styles.interestButtonActive]}
            onPress={handleNotInterested}
            disabled={isNotInterested}
          >
            <Text style={[styles.clearSearchButtonText, isNotInterested && styles.interestButtonActive]}>✕</Text>
            <Text style={[styles.interestButtonText, isNotInterested && styles.interestButtonActive]}>Not Interested</Text>
          </TouchableOpacity>
        </View>

      </View>
      {/* About Me Section - Always show this section */}
      <View style={styles.section}>
        <View style={styles.sectionTitleContainer}>
          <MaterialIcons name="person" size={22} color={variables.themeBGColor} />
          <Text style={styles.sectionTitle}>About Me</Text>
        </View>
        <Text style={styles.infoText}>{item.aboutMe || "Not updated"}</Text>
      </View>

      {/* Basic Details Section */}
      <View style={styles.section}>
        <View style={styles.sectionTitleContainer}>
          <MaterialIcons name="info" size={22} color={variables.themeBGColor} />
          <Text style={styles.sectionTitle}>Basic Details</Text>
        </View>
        <Text style={styles.infoText}>Age: {item.age || "Not updated"}</Text>
        <Text style={styles.infoText}>Date of Birth: {item.dateOfBirth || "Not updated"}</Text>
        <Text style={styles.infoText}>Gender: {item.gender || "Not updated"}</Text>
        <Text style={styles.infoText}>Height: {item.height ? `${item.height} cm` : "Not updated"}</Text>
        <Text style={styles.infoText}>Weight: {item.weight ? `${item.weight} kg` : "Not updated"}</Text>
        <Text style={styles.infoText}>Marital Status: {item.maritalStatus || "Not updated"}</Text>
        <Text style={styles.infoText}>Mother Tongue: {item.motherTongue || "Not updated"}</Text>
        <Text style={styles.infoText}>Physical Status: {item.physicalStatus || "Not updated"}</Text>
        <Text style={styles.infoText}>Star: {item.star || "Not updated"}</Text>
      </View>

      {/* Religious Details Section */}
      <View style={styles.section}>
        <View style={styles.sectionTitleContainer}>
          <MaterialCommunityIcons name="church" size={22} color={variables.themeBGColor} />
          <Text style={styles.sectionTitle}>Religious Details</Text>
        </View>
        <Text style={styles.infoText}>Religion: {
          (item.religion === 'Other' && item.customReligion) ?
          item.customReligion :
          (item.religion || "Not updated")
        }</Text>
        <Text style={styles.infoText}>Caste: {
          (item.caste === 'Other' && item.customCaste) ?
          item.customCaste :
          (item.caste || "Not updated")
        }</Text>
      </View>

      {/* Professional Details Section */}
      <View style={styles.section}>
        <View style={styles.sectionTitleContainer}>
          <MaterialIcons name="work" size={22} color={variables.themeBGColor} />
          <Text style={styles.sectionTitle}>Professional Details</Text>
        </View>
        <Text style={styles.infoText}>Education: {
          (item.highestEducation === 'Other' && item.customEducation) ?
          item.customEducation :
          (item.highestEducation || item.education || "Not updated")
        }</Text>
        <Text style={styles.infoText}>Occupation: {
          (item.occupation === 'Other' && item.customOccupation) ?
          item.customOccupation :
          (item.occupation || "Not updated")
        }</Text>
        <Text style={styles.infoText}>Salary: {item.salary || item.annualIncome || "Not updated"}</Text>
        <Text style={styles.infoText}>Employed In: {
          (item.employedIn === 'Other' && item.customEmployedIn) ?
          item.customEmployedIn :
          (item.employedIn || "Not updated")
        }</Text>
      </View>

      {/* Location Section */}
      <View style={styles.section}>
        <View style={styles.sectionTitleContainer}>
          <MaterialIcons name="location-on" size={22} color={variables.themeBGColor} />
          <Text style={styles.sectionTitle}>Location</Text>
        </View>
        <Text style={styles.infoText}>Country: {item.country || 'INDIA'}</Text>
        <Text style={styles.infoText}>State: {item.state || 'KERALA'}</Text>
        <Text style={styles.infoText}>District: {item.district || "Not updated"}</Text>
        <Text style={styles.infoText}>City: {item.city || "Not updated"}</Text>
      </View>

      {/* Habits & Lifestyle Section */}
      <View style={styles.section}>
        <View style={styles.sectionTitleContainer}>
          <MaterialCommunityIcons name="food-fork-drink" size={22} color={variables.themeBGColor} />
          <Text style={styles.sectionTitle}>Habits & Lifestyle</Text>
        </View>
        <Text style={styles.infoText}>Eating Habits: {item.eatingHabits || "Not updated"}</Text>
        <Text style={styles.infoText}>Drinking Habits: {item.drinkingHabits || "Not updated"}</Text>
        <Text style={styles.infoText}>Smoking Habits: {item.smokingHabits || "Not updated"}</Text>
      </View>

      {/* Contact Details Section */}
      <View style={styles.section}>
        <View style={styles.sectionTitleContainer}>
          <MaterialIcons name="contact-phone" size={22} color={variables.themeBGColor} />
          <Text style={styles.sectionTitle}>Contact Details</Text>
        </View>
        {usersIndex === slideIndex && isPhoneVisible ? (
          <Text style={styles.phoneInfoText}>Phone: {item.phoneNumber || "Not updated"}</Text>
        ) : (
          <TouchableOpacity onPress={handleViewPhoneNumberRequest} style={styles.viewPhoneButton}>
            <MaterialIcons name="phone" size={16} color="#007bff" />
            <Text style={styles.viewPhoneText}>View Phone Number</Text>
          </TouchableOpacity>
        )}
      </View>

      {/* Family Details Section - Always show */}
      <View style={styles.section}>
        <View style={styles.sectionTitleContainer}>
          <MaterialIcons name="family-restroom" size={22} color={variables.themeBGColor} />
          <Text style={styles.sectionTitle}>Family Details</Text>
        </View>
        <Text style={styles.infoText}>Father: {item.father || "Not updated"}</Text>
        <Text style={styles.infoText}>Mother: {item.mother || "Not updated"}</Text>
        <Text style={styles.infoText}>Siblings: {item.siblings || "Not updated"}</Text>
      </View>
    </ScrollView>
  );

  return (
    <View style={styles.container}>
      <Swiper
        index={usersIndex}
        loop={false}
        showsPagination={false}
        dotColor="#ccc"
        activeDotColor={variables.themeBGColor}
        onIndexChanged={(newIndex) => {
          checkIfFavourite(newIndex);
          checkIfNotInterested(newIndex);
          setIsPhoneVisible(false);
        }}
      >
        {users.map((user, index) => (
          <View key={user.userId} style={{ flex: 1 }}>
            {renderCarouselItem(user, index)}
          </View>
        ))}
      </Swiper>

      {/* Modal Popup for Payment and Plans */}
      <Modal visible={isModalVisible} animationType="slide" transparent={true}>
        <View style={modalStyles.modalBackground}>
          <View style={modalStyles.modalContainer}>
            <TouchableOpacity onPress={() => {
              setModalVisible(false)
              setPaymentContext("view")
            }} style={modalStyles.closeButton}>
              <Text style={modalStyles.closeButtonText}>✕</Text>
            </TouchableOpacity>
            <View style={modalStyles.tabHeader}>
              <TouchableOpacity
                onPress={() => setActiveTab('plans')}
                style={[
                  modalStyles.tabButton,
                  activeTab === 'plans' && modalStyles.activeTab,
                ]}
              >
                <Text style={modalStyles.tabButtonText}>Plans</Text>
              </TouchableOpacity>
              <TouchableOpacity
                onPress={() => setActiveTab('payment')}
                style={[
                  modalStyles.tabButton,
                  activeTab === 'payment' && modalStyles.activeTab,
                ]}
              >
                <Text style={modalStyles.tabButtonText}>Payment</Text>
              </TouchableOpacity>
            </View>
            <View style={modalStyles.tabContent}>
              {activeTab === 'plans' ? (
                <View>
                  <Text style={modalStyles.infoText}>Choose a Plan:</Text>
                  <View style={modalStyles.plansContainer}>
                    {fetchedPlans ? (
                      Object.keys(fetchedPlans).map((key) => {
                        const rangeStr = fetchedPlans[key]; // e.g., "25-500"
                        const [minVal, maxVal] = rangeStr.split('-').map(val => Number(val));
                        return (
                          <Pressable
                            key={key}
                            onPress={() => {
                              setSelectedPlanRange({ min: minVal, max: maxVal, planDays: Number(key) });
                              setActiveTab('payment');
                            }}
                            style={({ pressed, hovered }) => [
                              modalStyles.planCard,
                              (pressed || hovered) && modalStyles.planCardActive,
                            ]}
                          >
                            <Text style={modalStyles.planTitle}>{key} Days Plan</Text>
                            <Text style={modalStyles.planPrice}>₹{rangeStr}</Text>
                          </Pressable>
                        );
                      })
                    ) : (
                      <Text>Loading plans...</Text>
                    )}
                  </View>
                  {/* Note message */}
                  <Text style={modalStyles.modalNote}>
                    Offer: Unlock one user's contact for just ₹{minAmount}
                  </Text>
                </View>
              ) : (
                <View>
                  {selectedPlanRange ? (
                    <Text style={modalStyles.infoText}>
                      Please enter your plan amount.
                    </Text>
                  ) : (
                    <Text style={modalStyles.infoText}>Enter your plan amount:</Text>
                  )}
                  <TextInput
                    style={modalStyles.input}
                    placeholder="Enter amount"
                    keyboardType="numeric"
                    value={Number(amount)}
                    onChangeText={text => {
                      const numericValue = Number(text);
                      if (numericValue > 2500) {
                        setErrorMessage("Amount cannot be greater than ₹2500.");
                      } else {
                        setErrorMessage("");
                      }
                      setAmount(numericValue);
                    }}
                  />
                  {errorMessage ? <Text style={{ color: "red", marginTop: -15, marginBottom: 20, fontSize: 12 }}>{errorMessage}</Text> : null}
                  <TouchableOpacity style={modalStyles.submitButton} onPress={handlePayment}>
                    <Text style={modalStyles.submitButtonText}>Submit Payment</Text>
                  </TouchableOpacity>
                </View>
              )}
            </View>
          </View>
        </View>
      </Modal>

      {isLoading && (
        <View style={styles.loaderContainer}>
          <ActivityIndicator size="large" color={variables.themeBGColor} />
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#f8f9fa', position: 'relative' },
  scrollContainer: {
    alignItems: 'center',
    paddingTop: 12,
    paddingBottom: 24,
    backgroundColor: '#f8f9fa',
  },
  profileImage: {
    width: screenWidth * 0.9,
    height: screenWidth * 1.2,
    borderRadius: 10,
    resizeMode: 'cover',
  },
  overlayContainer: {
    position: 'absolute',
    bottom: 0,
    width: screenWidth * 0.9,
    borderRadius: 10,
    overflow: 'hidden',
  },
  gradient: {
    padding: 20,
    borderRadius: 10,
    alignItems: 'center',
  },
  userName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
    textShadowColor: 'rgba(0, 0, 0, 0.5)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 3,
  },
  userDetail: {
    fontSize: 16,
    color: '#fff',
    marginBottom: 60,
    textShadowColor: 'rgba(0, 0, 0, 0.5)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 3,
  },
  interestButtonsContainer: {
    position: 'absolute',
    bottom: 20,
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
  },
  interestButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
    borderRadius: 25,
    paddingLeft: 20,
    paddingRight: 20,
    marginRight: 35,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  interestButtonActive: {
    color: 'red',
    fontWeight: 'bold',
  },
  interestButtonText: {
    color: '#fff',
    marginLeft: 5,
    fontWeight: '600',
  },
  clearSearchButtonText: {
    fontSize: 16,
    color: '#fff',
    fontWeight: 'bold',
  },
  favButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
    paddingLeft: 20,
    paddingRight: 20,
    borderRadius: 25,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  favButtonText: {
    color: '#fff',
    fontSize: 25,
  },
  basicInfo: { fontSize: 16, color: '#34495e', marginBottom: 10, textAlign: 'center' },
  section: {
    width: '90%',
    marginTop: 15,
    marginBottom: 5,
    backgroundColor: '#ffffff',
    borderRadius: 7,
    padding: 16,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.2,
        shadowRadius: 4,
      },
      android: {
        elevation: 2,
      },
    }),
  },
  sectionTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    paddingBottom: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: variables.inputTextColor,
    marginLeft: 8,
  },
  infoText: {
    fontSize: 14,
    color: '#4a4a4a',
    marginBottom: 8,
    lineHeight: 20,
    paddingHorizontal: 4,
  },
  phoneInfoText: {
    fontSize: 14,
    color: '#4a4a4a',
    marginBottom: 8,
    fontWeight: '500',
    paddingHorizontal: 4,
  },
  viewPhoneButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
    marginBottom: 8,
    backgroundColor: '#f8f9fa',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    alignSelf: 'flex-start',
  },
  viewPhoneText: {
    fontSize: 14,
    color: '#007bff',
    fontWeight: '600',
    marginLeft: 5,
  },
  loaderContainer: {
    position: 'absolute',
    top: 0, left: 0, right: 0, bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
});

const modalStyles = StyleSheet.create({
  modalBackground: {
    flex: 1, backgroundColor: 'rgba(0,0,0,0.5)', justifyContent: 'center', alignItems: 'center',
  },
  modalContainer: {
    width: '90%', backgroundColor: '#fff', borderRadius: 10, padding: 20, maxHeight: '85%',
  },
  closeButton: { alignSelf: 'flex-end' },
  closeButtonText: { fontSize: 25, fontWeight: 'bold', color: '#000' },
  modalNote: {
    fontSize: 14,
    //fontWeight: '600',
    color: '#ff1f00', // Elegant accent color
    textAlign: 'center',
    marginTop: 15,
    marginBottom: 10,
    //backgroundColor: '#f39c12', // Bright orange background
    //paddingVertical: 10,
    //paddingHorizontal: 15,
    borderRadius: 10,
  },
  tabHeader: { flexDirection: 'row', marginVertical: 10 },
  tabButton: { flex: 1, paddingVertical: 10, alignItems: 'center', borderBottomWidth: 2, borderBottomColor: '#ccc' },
  activeTab: { borderBottomColor: variables.themeBGColor, borderBottomWidth: 4 },
  tabButtonText: { fontSize: 18, fontWeight: 'bold' },
  tabContent: { marginTop: 20 },
  infoText: { fontSize: 17, marginBottom: 10, textAlign: 'left' },
  input: { borderWidth: 1, borderColor: '#ccc', borderRadius: 8, padding: 10, marginBottom: 20, textAlign: 'left' },
  submitButton: { backgroundColor: variables.themeBGColor, paddingVertical: 15, marginTop: 40, marginBottom: 10, borderRadius: 8, alignItems: 'center' },
  submitButtonText: { color: '#fff', fontSize: 17 },
  plansContainer: { marginTop: 10 },
  planCard: {
    backgroundColor: '#f8f9fa',
    borderRadius: 10,
    padding: 25,
    marginBottom: 10,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  planCardActive: { backgroundColor: '#e0e0e0' },
  planTitle: { fontSize: 18, fontWeight: 'bold', color: variables.inputTextColor },
  planPrice: { fontSize: 18, color: variables.themeBGColor, fontWeight: 'bold' },
});

export default ProfileDetailScreen;