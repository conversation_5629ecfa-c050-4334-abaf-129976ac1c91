import notifee, { AndroidImportance } from '@notifee/react-native';
import messaging from '@react-native-firebase/messaging';
import { Alert, PermissionsAndroid, Platform } from 'react-native';

export const displayNotification = async (remoteMessage) => {
  await notifee.displayNotification({
    title: remoteMessage.notification?.title || 'New Like!',
    body: remoteMessage.notification?.body || 'Someone liked your profile!',
    data: {
      screen: 'LikedByOthers', // Add navigation data
      ...remoteMessage.data, // Include any additional data from FCM
    },
    android: {
      channelId: 'likedbyothers',
      pressAction: {
        id: 'likedbyothers_action', // Change from 'default' to specific action
      },
    },
  });
};

export const requestPermission = async () => {
  if (Platform.OS === 'android') {
    const granted = await PermissionsAndroid.request(
      PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS
    );
  }
  await messaging().requestPermission();
};

export const createNotificationChannel = async () => {
  await notifee.createChannel({
    id: 'likedbyothers',
    name: 'Profile Likes', // Shorter, cleaner name
    description: 'Notifications when someone likes your profile',
    importance: AndroidImportance.HIGH,
  });
};