import notifee, { AndroidImportance } from '@notifee/react-native';
import messaging from '@react-native-firebase/messaging';
import { Alert, PermissionsAndroid, Platform } from 'react-native';

export const displayNotification = async (remoteMessage) => {
  // Only display notification when app is in foreground
  // FCM handles background notifications automatically
  const { AppState } = require('react-native');

  if (AppState.currentState === 'active') {
    await notifee.displayNotification({
      title: remoteMessage.notification?.title || 'New Like!',
      body: remoteMessage.notification?.body || 'Someone liked your profile!',
      data: {
        screen: 'LikedByOthers',
        type: 'profile_like',
        ...remoteMessage.data,
      },
      android: {
        channelId: 'likedbyothers',
        pressAction: {
          id: 'likedbyothers_action',
        },
      },
    });
  }
};

export const requestPermission = async () => {
  if (Platform.OS === 'android') {
    const granted = await PermissionsAndroid.request(
      PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS
    );
  }
  await messaging().requestPermission();
};

export const createNotificationChannel = async () => {
  await notifee.createChannel({
    id: 'likedbyothers',
    name: 'Profile Likes',
    description: 'Notifications when someone likes your profile',
    importance: AndroidImportance.HIGH,
  });
};