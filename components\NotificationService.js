import notifee, { AndroidImportance } from '@notifee/react-native';
import messaging from '@react-native-firebase/messaging';
import { Alert, PermissionsAndroid, Platform } from 'react-native';

export const displayNotification = async (remoteMessage) => {
  // Only display local notification if the app is in foreground
  // FCM handles background notifications automatically
  const appState = require('react-native').AppState;

  if (appState.currentState === 'active') {
    await notifee.displayNotification({
      title: remoteMessage.notification?.title || 'New Like!',
      body: remoteMessage.notification?.body || 'Someone liked your profile!',
      data: {
        screen: 'LikedByOthers',
        type: 'profile_like',
        ...remoteMessage.data,
      },
      android: {
        channelId: 'likedbyothers',
        pressAction: {
          id: 'likedbyothers_action',
        },
      },
    });
  }
};

export const requestPermission = async () => {
  if (Platform.OS === 'android') {
    const granted = await PermissionsAndroid.request(
      PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS
    );
  }
  await messaging().requestPermission();
};

export const createNotificationChannel = async () => {
  await notifee.createChannel({
    id: 'likedbyothers',
    name: 'Profile Likes', // Shorter, cleaner name
    description: 'Notifications when someone likes your profile',
    importance: AndroidImportance.HIGH,
  });
};