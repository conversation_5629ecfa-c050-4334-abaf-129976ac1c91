import notifee, { AndroidImportance } from '@notifee/react-native';
import messaging from '@react-native-firebase/messaging';
import { Alert, PermissionsAndroid, Platform } from 'react-native';

export const displayNotification = async (remoteMessage) => {
  await notifee.displayNotification({
    title: remoteMessage.notification?.title || 'Default Title',
    body: remoteMessage.notification?.body || 'Default Body',
    android: {
      channelId: 'likedbyothers',
      pressAction: {
        id: 'default',
      },
    },
  });
};

export const requestPermission = async () => {
  if (Platform.OS === 'android') {
    const granted = await PermissionsAndroid.request(
      PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS
    );
  }
  await messaging().requestPermission();
};

export const createNotificationChannel = async () => {
  await notifee.createChannel({
    id: 'likedbyothers',
    name: 'Liked by Others Channel',
    importance: AndroidImportance.HIGH,
  });
};